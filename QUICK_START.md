# 快速开始指南

## 项目简介

医学研究管理系统的HeaderList控件，基于JavaFX开发，采用医学专业UI设计。

## 核心组件

- **TitledListPanel** - 带标题的基础列表面板
- **EthicsReviewListPanel** - 伦理审查专用列表面板  
- **EthicsReviewProject** - 伦理审查项目数据模型
- **EthicsReviewProjectRenderer** - 项目列表渲染器

## 快速运行

### 编译项目
```
mvn clean compile
```

### 运行应用
```
mvn javafx:run
```

### 运行仪表板
```
mvn javafx:run@dashboard
```

## 项目结构

```
src/main/java/com/fox/med/qhcg/
├── model/
│   └── EthicsReviewProject.java
├── laf/
│   ├── components/
│   ├── renderers/
│   └── panels/
├── MedResearchApp.java
├── DashboardApp.java
└── TestImprovedHeaderList.java
```

## 技术栈

- Java 17
- JavaFX 21.0.1
- Maven
- CSS样式系统

## 特性

- 医学专业主题设计
- 7种审核状态可视化
- 响应式布局
- 数据绑定支持

## 使用示例

创建基础列表：
```
TitledListPanel list = new TitledListPanel("科室列表");
list.addItems("心内科", "神经内科");
```

创建项目列表：
```
EthicsReviewListPanel projectList = new EthicsReviewListPanel("项目列表");
projectList.setCellFactory(listView -> new EthicsReviewProjectRenderer());
```

## 状态系统

支持以下审核状态：
- 待审核 (橙色)
- 已通过 (绿色)  
- 已拒绝 (红色)
- 审核中 (蓝色)
- 暂停 (灰色)

## 开发环境

确保安装：
- JDK 17+
- Maven 3.6+
- JavaFX运行时

## 构建说明

项目使用Maven构建，包含完整的JavaFX模块配置。所有依赖会自动下载。

## 样式定制

CSS样式文件位于：
```
src/main/resources/styles/header-list.css
```

包含272行医学专业主题样式定义。
