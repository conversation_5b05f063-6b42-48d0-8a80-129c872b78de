# 医学研究管理系统

## 项目简介

基于JavaFX的医学研究管理系统HeaderList控件，采用医学专业UI设计规范。

## 主要特性

- 医学专业主题设计
- 7种审核状态可视化
- 响应式布局
- 数据绑定支持

## 核心组件

- TitledListPanel - 带标题的基础列表面板
- EthicsReviewListPanel - 伦理审查专用列表面板
- EthicsReviewProject - 伦理审查项目数据模型
- EthicsReviewProjectRenderer - 项目列表渲染器
- EthicsReviewDashboardPanel - 主仪表板面板

## 快速开始

### 编译项目
mvn clean compile

### 运行应用
mvn javafx:run

### 运行仪表板
mvn javafx:run@dashboard

### 运行测试
mvn javafx:run@test-improved

## 使用方法

### 创建基础列表
TitledListPanel list = new TitledListPanel("标题");
list.addItems("项目1", "项目2", "项目3");

### 创建项目列表
EthicsReviewListPanel projectList = new EthicsReviewListPanel("项目列表");
projectList.setCellFactory(listView -> new EthicsReviewProjectRenderer());

### 添加项目数据
EthicsReviewProject project = new EthicsReviewProject("标题", "研究者", "状态", "日期", "科室");
projectList.addItem(project);

## 项目结构

src/main/java/com/fox/med/qhcg/
- model/ - 数据模型
- laf/ - Look and Feel组件
  - components/ - UI组件
  - renderers/ - 渲染器
  - panels/ - 面板
- MedResearchApp.java - 基础示例
- DashboardApp.java - 仪表板应用
- TestImprovedHeaderList.java - 测试应用

## 技术栈

- Java 17
- JavaFX 21.0.1
- Maven
- CSS样式系统

## 状态系统

支持以下审核状态：
- 待审核 (橙色主题)
- 已通过 (绿色主题)
- 已拒绝 (红色主题)
- 审核中 (蓝色主题)
- 暂停 (灰色主题)

## 样式定制

CSS样式文件位于 src/main/resources/styles/header-list.css
包含272行医学专业主题样式定义

## 开发环境要求

- JDK 17或更高版本
- Maven 3.6或更高版本
- JavaFX运行时环境

## 构建说明

项目使用Maven构建系统，包含完整的JavaFX模块配置。
运行 mvn clean compile 即可自动下载所有依赖。
