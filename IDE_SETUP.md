# IDE配置指南 - JavaFX开发环境

## 🎯 为什么使用JavaFX

JavaFX是现代Java应用程序的最佳UI框架选择，特别适合医学研究管理系统：

### ✅ JavaFX优势
- **现代化设计**: 比Swing更美观、更专业
- **CSS样式支持**: 完美支持我们的医学专业主题
- **丰富的控件**: 适合复杂的医学数据展示
- **数据绑定**: 优秀的MVVM模式支持
- **跨平台**: 一次编写，到处运行

## 🔧 IDE配置步骤

### IntelliJ IDEA配置

1. **项目设置**
   ```
   File -> Project Structure -> Project
   - Project SDK: Java 17
   - Project language level: 17
   ```

2. **模块设置**
   ```
   File -> Project Structure -> Modules
   - 确保src/main/java被标记为Sources
   - 确保src/main/resources被标记为Resources
   ```

3. **JavaFX库配置**
   ```
   File -> Project Structure -> Libraries
   - 添加Maven依赖: org.openjfx:javafx-controls:21.0.1
   - 添加Maven依赖: org.openjfx:javafx-fxml:21.0.1
   ```

4. **运行配置**
   ```
   Run -> Edit Configurations -> Add New -> Application
   - Main class: com.fox.med.qhcg.MedResearchApp
   - VM options: --module-path "path/to/javafx/lib" --add-modules javafx.controls,javafx.fxml
   ```

### Eclipse配置

1. **项目属性**
   ```
   右键项目 -> Properties -> Java Build Path
   - 确保使用Java 17
   - 添加JavaFX库到Classpath
   ```

2. **模块路径**
   ```
   Properties -> Java Build Path -> Module Path
   - 添加JavaFX模块
   ```

### VS Code配置

1. **扩展安装**
   ```
   - Extension Pack for Java
   - JavaFX Support
   ```

2. **launch.json配置**
   ```json
   {
       "type": "java",
       "name": "MedResearchApp",
       "request": "launch",
       "mainClass": "com.fox.med.qhcg.MedResearchApp",
       "vmArgs": "--module-path path/to/javafx/lib --add-modules javafx.controls,javafx.fxml"
   }
   ```

## 🚀 快速启动方法

### 方法1: 使用Maven (推荐)
```bash
mvn clean compile
mvn javafx:run
```

### 方法2: 使用启动脚本
```bash
# Windows
run-javafx.bat

# Linux/Mac
chmod +x run-javafx.sh
./run-javafx.sh
```

### 方法3: 直接Java命令
```bash
java --module-path "path/to/javafx/lib" --add-modules javafx.controls,javafx.fxml -cp target/classes com.fox.med.qhcg.MedResearchApp
```

## 🔍 常见问题解决

### 问题1: "无法解析符号 'javafx'"
**解决方案**: 
- 确保Maven依赖正确下载
- 刷新IDE项目
- 重新导入Maven项目

### 问题2: "模块未找到"
**解决方案**:
- 检查module-info.java文件
- 确保JavaFX在模块路径中
- 使用--add-modules参数

### 问题3: "运行时错误"
**解决方案**:
- 检查JavaFX运行时是否正确安装
- 使用正确的VM参数
- 确保所有依赖都在类路径中

## 📋 验证配置

运行以下命令验证配置是否正确：

```bash
# 1. 编译测试
mvn clean compile

# 2. 运行数据模型测试
java -cp target/classes com.fox.med.qhcg.TestDataModel

# 3. 运行JavaFX应用
mvn javafx:run
```

如果所有命令都成功执行，说明配置正确！

## 🎨 我们的JavaFX成果

使用JavaFX，我们已经实现了：

- ✅ **272行专业CSS样式** - 医学主题设计
- ✅ **完整的HeaderList控件系列** - 现代化UI组件
- ✅ **7种状态可视化系统** - 专业的状态管理
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **数据绑定支持** - 优秀的MVVM架构

这些都是JavaFX独有的优势，是Swing无法比拟的！
