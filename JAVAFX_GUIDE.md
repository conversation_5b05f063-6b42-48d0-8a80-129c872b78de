# JavaFX使用指南 - 医学研究管理系统

## 🎯 为什么选择JavaFX而不是其他框架

### JavaFX vs Swing

| 特性 | JavaFX | Swing |
|------|--------|-------|
| **现代化程度** | ✅ 现代化设计 | ❌ 过时的外观 |
| **CSS支持** | ✅ 完整CSS支持 | ❌ 有限的样式定制 |
| **数据绑定** | ✅ 强大的属性绑定 | ❌ 手动事件处理 |
| **动画效果** | ✅ 内置动画支持 | ❌ 需要第三方库 |
| **移动端支持** | ✅ 支持移动设备 | ❌ 仅桌面应用 |
| **开发效率** | ✅ FXML + CSS | ❌ 纯代码布局 |

### 我们的JavaFX成果

使用JavaFX，我们已经实现了：

- ✅ **272行专业CSS样式** - 医学主题设计
- ✅ **完整的HeaderList控件系列** - 现代化UI组件
- ✅ **7种状态可视化系统** - 专业的状态管理
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **数据绑定支持** - 优秀的MVVM架构

## 🚀 快速启动JavaFX应用

### 方法1: Maven命令 (推荐)

```bash
# 编译项目
mvn clean compile

# 运行主应用
mvn javafx:run

# 运行仪表板
mvn javafx:run@dashboard

# 运行改进版测试
mvn javafx:run@test-improved
```

### 方法2: 使用启动脚本

```bash
# Windows
run-javafx.bat

# 这个脚本会自动：
# 1. 编译项目
# 2. 启动JavaFX应用
# 3. 如果失败，尝试备用方法
```

### 方法3: IDE直接运行

在您的IDE中：
1. 打开 `MedResearchApp.java`
2. 右键 -> Run
3. 如果出现模块错误，添加VM参数：
   ```
   --module-path "path/to/javafx/lib" --add-modules javafx.controls,javafx.fxml
   ```

## 🎨 JavaFX应用展示

### 1. 基础HeaderList演示 (MedResearchApp)

展示内容：
- 基础HeaderList控件
- 医学研究专用HeaderList
- 自定义单元格显示
- 医学专业主题样式

### 2. 完整仪表板 (DashboardApp)

展示内容：
- 多个HeaderList控件组合
- 统计信息卡片
- 操作按钮区域
- 完整的医学研究管理界面

### 3. 改进版测试 (TestImprovedHeaderList)

展示内容：
- 改进后的所有控件
- 医学专业色彩方案
- 增强的状态可视化
- 优化的用户体验

## 🔧 JavaFX配置详情

### Maven依赖配置

```xml
<dependencies>
    <!-- JavaFX Controls -->
    <dependency>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-controls</artifactId>
        <version>21.0.1</version>
    </dependency>
    
    <!-- JavaFX FXML -->
    <dependency>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-fxml</artifactId>
        <version>21.0.1</version>
    </dependency>
</dependencies>
```

### 模块系统配置

```java
// module-info.java
module medresearchmanager {
    requires javafx.controls;
    requires javafx.fxml;
    requires java.desktop;
    
    exports com.fox.med.qhcg;
    exports com.fox.med.qhcg.model;
    exports com.fox.med.qhcg.ui.controls;
    exports com.fox.med.qhcg.ui.cells;
    exports com.fox.med.qhcg.ui;
}
```

### JavaFX Maven插件

```xml
<plugin>
    <groupId>org.openjfx</groupId>
    <artifactId>javafx-maven-plugin</artifactId>
    <version>0.0.8</version>
    <configuration>
        <mainClass>com.fox.med.qhcg.MedResearchApp</mainClass>
    </configuration>
</plugin>
```

## 🎯 JavaFX特色功能展示

### 1. CSS样式系统

我们的医学专业主题CSS：

```css
/* 医学专业主题色彩 */
.research-header-list {
    -fx-background-color: #ffffff;
    -fx-border-color: #cbd5e1;
    -fx-border-radius: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.12), 8, 0, 0, 3);
}

/* 状态可视化 */
.status-approved {
    -fx-background-color: #d1fae5;
    -fx-text-fill: #065f46;
    -fx-border-color: #10b981;
}
```

### 2. 数据绑定示例

```java
// JavaFX属性绑定
private final StringProperty title = new SimpleStringProperty();
private final StringProperty status = new SimpleStringProperty();

// 自动更新UI
titleLabel.textProperty().bind(item.titleProperty());
statusLabel.textProperty().bind(item.statusProperty());
```

### 3. 自定义控件

```java
// 医学研究专用HeaderList
ResearchHeaderList<ResearchItem> list = new ResearchHeaderList<>("伦理审查项目");
list.setCellFactory(listView -> new ResearchItemCell());
list.addItem(new ResearchItem(...));
```

## 🔍 常见问题解决

### Q: IDE显示"无法解析符号 'javafx'"
**A**: 
1. 确保Maven依赖已下载：`mvn dependency:resolve`
2. 刷新IDE项目
3. 检查module-info.java文件

### Q: 运行时出现模块错误
**A**: 
1. 使用Maven命令：`mvn javafx:run`
2. 或添加VM参数：`--module-path ... --add-modules javafx.controls,javafx.fxml`

### Q: 应用启动但没有窗口
**A**: 
1. 检查主类是否正确
2. 确保JavaFX运行时已安装
3. 查看控制台错误信息

## 📈 JavaFX的未来发展

JavaFX是Java生态系统中UI开发的未来：

- **持续更新**: Oracle和OpenJDK社区持续维护
- **性能优化**: 不断改进的渲染性能
- **跨平台**: 支持Windows、macOS、Linux
- **移动支持**: 通过Gluon Mobile支持移动设备
- **Web支持**: 通过JPro支持Web部署

选择JavaFX，就是选择了现代化、专业化的Java UI开发方案！
