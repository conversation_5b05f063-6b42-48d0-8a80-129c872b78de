<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fox.med.qhcg</groupId>
    <artifactId>MedResearchManager</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <javafx.version>21.0.2</javafx.version>
        <javafx.maven.plugin.version>0.0.8</javafx.maven.plugin.version>
        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
    </properties>

    <dependencies>
        <!-- JavaFX Controls -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>${javafx.version}</version>
        </dependency>

        <!-- JavaFX FXML -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>${javafx.version}</version>
        </dependency>

        <!-- JavaFX Base -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-base</artifactId>
            <version>${javafx.version}</version>
        </dependency>

        <!-- JavaFX Graphics -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-graphics</artifactId>
            <version>${javafx.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>${javafx.maven.plugin.version}</version>
                <configuration>
                    <mainClass>com.fox.med.qhcg.MedResearchApp</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <configuration>
                            <mainClass>com.fox.med.qhcg.MedResearchApp</mainClass>
                        </configuration>
                    </execution>
                    <execution>
                        <id>dashboard</id>
                        <configuration>
                            <mainClass>com.fox.med.qhcg.DashboardApp</mainClass>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-improved</id>
                        <configuration>
                            <mainClass>com.fox.med.qhcg.TestImprovedHeaderList</mainClass>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flat-table</id>
                        <configuration>
                            <mainClass>com.fox.med.qhcg.FlatTableTestApp</mainClass>
                        </configuration>
                    </execution>
                    <execution>
                        <id>independent-table</id>
                        <configuration>
                            <mainClass>com.fox.med.qhcg.IndependentFlatTableApp</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


        </plugins>
    </build>

</project>