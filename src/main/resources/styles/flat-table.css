/* 医学研究管理系统 - FlatTable 组件样式 */
/* 扁平化表格组件的专用样式 */

/* ==================== FlatTable简洁样式 ==================== */

/* 扁平化表格容器 */
.flat-table-container {
    -fx-background-color: #ffffff;
    -fx-padding: 0;
}

/* 扁平化表格 */
.flat-table {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-table-cell-border-color: #e2e8f0;
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -fx-font-size: 14px;
}

/* 表格列标题 */
.flat-table .column-header {
    -fx-background-color: #f1f5f9;
    -fx-border-color: transparent transparent #cbd5e1 transparent;
    -fx-border-width: 0 0 1px 0;
    -fx-font-weight: 600;
    -fx-text-fill: #475569;
    -fx-font-size: 14px;
    -fx-padding: 12 16 12 16;
}

.flat-table .column-header .label {
    -fx-font-weight: 600;
    -fx-text-fill: #475569;
}

/* 表格行 */
.flat-table-row {
    -fx-background-color: transparent;
    -fx-border-color: transparent transparent #f1f5f9 transparent;
    -fx-border-width: 0 0 1px 0;
}

.flat-table-row:hover {
    -fx-background-color: #f8fafc;
}

.flat-table-row:selected {
    -fx-background-color: #e0e7ff;
    -fx-text-fill: #3730a3;
}

/* 表格单元格 */
.flat-table .table-cell {
    -fx-border-color: transparent;
    -fx-padding: 12 16 12 16;
    -fx-text-fill: #374151;
    -fx-font-size: 14px;
    -fx-alignment: CENTER-LEFT;
}

/* ==================== 表格列专用样式 ==================== */

/* 序号列样式 */
.flat-column-number .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 500;
    -fx-text-fill: #6b7280;
    -fx-font-size: 13px;
}

/* 名称列样式 */
.flat-column-name .table-cell {
    -fx-alignment: CENTER-LEFT;
    -fx-font-weight: 500;
    -fx-text-fill: #1f2937;
}

/* 权重列样式 */
.flat-column-weight .table-cell {
    -fx-alignment: CENTER;
    -fx-font-family: "Consolas", "Monaco", monospace;
    -fx-text-fill: #059669;
    -fx-font-weight: 500;
}

/* 符号列样式 */
.flat-column-symbol .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 600;
    -fx-text-fill: #7c3aed;
    -fx-font-size: 16px;
}

/* ==================== 带标题的FlatTable样式 ==================== */

/* 扁平化表格面板 */
.flat-table-panel {
    -fx-background-color: #ffffff;
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1.5px;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.12), 8, 0, 0, 3);
    -fx-padding: 0;
}

/* 扁平化表格标题 */
.flat-table-title {
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e3a8a;
    -fx-background-color: #f8fafc;
    -fx-border-color: transparent transparent #e2e8f0 transparent;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 20 20 16 20;
}

/* 扁平化表格视图 */
.flat-table-view {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-table-cell-border-color: #e2e8f0;
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -fx-font-size: 14px;
}

/* 带标题表格的列标题 */
.flat-table-view .column-header {
    -fx-background-color: #f1f5f9;
    -fx-border-color: transparent transparent #cbd5e1 transparent;
    -fx-border-width: 0 0 2px 0;
    -fx-font-weight: 600;
    -fx-text-fill: #475569;
    -fx-font-size: 14px;
    -fx-padding: 12 16 12 16;
}

.flat-table-view .column-header .label {
    -fx-font-weight: 600;
    -fx-text-fill: #475569;
}

/* 带标题表格的单元格 */
.flat-table-view .table-cell {
    -fx-border-color: transparent;
    -fx-padding: 12 16 12 16;
    -fx-text-fill: #374151;
    -fx-font-size: 14px;
    -fx-alignment: CENTER-LEFT;
}

/* 带标题表格的列样式 */
.flat-table-column-number .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 500;
    -fx-text-fill: #6b7280;
    -fx-font-size: 13px;
}

.flat-table-column-name .table-cell {
    -fx-alignment: CENTER-LEFT;
    -fx-font-weight: 500;
    -fx-text-fill: #1f2937;
}

.flat-table-column-weight .table-cell {
    -fx-alignment: CENTER;
    -fx-font-family: "Consolas", "Monaco", monospace;
    -fx-text-fill: #059669;
    -fx-font-weight: 500;
}

.flat-table-column-symbol .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 600;
    -fx-text-fill: #7c3aed;
    -fx-font-size: 16px;
}
