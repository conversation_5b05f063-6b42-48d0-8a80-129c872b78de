/* 医学研究管理系统 - 响应式设计样式 */
/* 适配不同屏幕尺寸的响应式样式 */

/* ==================== 移动设备适配 ==================== */
@media (max-width: 768px) {
    /* HeaderList响应式 */
    .header-list-title {
        -fx-font-size: 16px;
        -fx-padding: 16 16 12 16;
    }
    
    .header-list-view .list-cell {
        -fx-padding: 10px 12px;
        -fx-font-size: 13px;
    }
    
    /* EthicsReviewList响应式 */
    .research-header-title {
        -fx-font-size: 16px;
    }

    .research-item-title {
        -fx-font-size: 14px;
    }
    
    .research-item-researcher {
        -fx-font-size: 12px;
    }
    
    .research-item-status {
        -fx-font-size: 11px;
        -fx-padding: 3 8 3 8;
    }
    
    .research-item-date {
        -fx-font-size: 11px;
    }
    
    /* FlatTable响应式 */
    .flat-table-panel {
        -fx-min-width: 400px;
    }
    
    .flat-table-title {
        -fx-font-size: 16px;
        -fx-padding: 16 16 12 16;
    }

    .flat-table-view .table-cell,
    .flat-table .table-cell {
        -fx-font-size: 13px;
        -fx-padding: 10 12 10 12;
    }
    
    .flat-table-view .column-header,
    .flat-table .column-header {
        -fx-font-size: 13px;
        -fx-padding: 10 12 10 12;
    }
}

/* ==================== 平板设备适配 ==================== */
@media (max-width: 1024px) and (min-width: 769px) {
    .header-list-title,
    .research-header-title,
    .flat-table-title {
        -fx-font-size: 17px;
    }
    
    .flat-table-panel {
        -fx-min-width: 500px;
    }
}

/* ==================== 大屏幕适配 ==================== */
@media (min-width: 1440px) {
    .header-list-title,
    .research-header-title,
    .flat-table-title {
        -fx-font-size: 20px;
    }
    
    .research-item-title {
        -fx-font-size: 16px;
    }
    
    .flat-table-view .table-cell,
    .flat-table .table-cell {
        -fx-font-size: 15px;
    }
}

/* ==================== 高DPI屏幕适配 ==================== */
@media (-fx-scale-x: 1.5), (-fx-scale-y: 1.5) {
    .header-list,
    .research-header-list,
    .flat-table-panel {
        -fx-border-width: 2px;
    }
    
    .scroll-bar:vertical {
        -fx-pref-width: 12px;
    }
}
