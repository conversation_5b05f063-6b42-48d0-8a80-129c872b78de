.research-header-list {
    -fx-background-color: #ffffff;
    -fx-border-color: #e2e8f0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.research-header-title {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e3a8a;
}

.research-header-count {
    -fx-font-size: 12px;
    -fx-text-fill: #64748b;
    -fx-background-color: #f1f5f9;
    -fx-padding: 2 6;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.research-list-view {
    -fx-background-color: transparent;
    -fx-focus-color: transparent;
    -fx-padding: 6 0;
}

.research-list-view .list-cell {
    -fx-padding: 0;
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #f1f5f9;
}

.research-list-view .list-cell:selected {
    -fx-background-color: #ecfdf5;
}

.research-list-view .list-cell:hover {
    -fx-background-color: #f8fafc;
}

.research-item-title {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e293b;
    -fx-wrap-text: true;
}

.research-item-researcher {
    -fx-font-size: 12px;
    -fx-text-fill: #475569;
}

.research-item-status {
    -fx-font-size: 11px;
    -fx-font-weight: 600;
    -fx-padding: 2 8;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
}

.research-item-date {
    -fx-font-size: 11px;
    -fx-text-fill: #64748b;
}

.research-header-box {
    -fx-padding: 16 16 12 16;
    -fx-background-color: #f8fafc;
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #e2e8f0;
}
