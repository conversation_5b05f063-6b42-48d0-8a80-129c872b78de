/* 医学研究管理系统 - 基础样式 */
/* 全局字体、颜色和通用样式定义 */

/* ==================== 全局字体设置 ==================== */
.root {
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

/* ==================== 医学专业状态样式 ==================== */
.status-pending {
    -fx-background-color: #fef3c7;
    -fx-text-fill: #92400e;
    -fx-border-color: #f59e0b;
    -fx-effect: dropshadow(three-pass-box, rgba(245,158,11,0.2), 2, 0, 0, 1);
}

.status-approved {
    -fx-background-color: #d1fae5;
    -fx-text-fill: #065f46;
    -fx-border-color: #10b981;
    -fx-effect: dropshadow(three-pass-box, rgba(16,185,129,0.2), 2, 0, 0, 1);
}

.status-rejected {
    -fx-background-color: #fee2e2;
    -fx-text-fill: #991b1b;
    -fx-border-color: #ef4444;
    -fx-effect: dropshadow(three-pass-box, rgba(239,68,68,0.2), 2, 0, 0, 1);
}

.status-reviewing {
    -fx-background-color: #e0e7ff;
    -fx-text-fill: #3730a3;
    -fx-border-color: #6366f1;
    -fx-effect: dropshadow(three-pass-box, rgba(99,102,241,0.2), 2, 0, 0, 1);
}

.status-suspended {
    -fx-background-color: #f3f4f6;
    -fx-text-fill: #374151;
    -fx-border-color: #6b7280;
    -fx-effect: dropshadow(three-pass-box, rgba(107,114,128,0.2), 2, 0, 0, 1);
}

/* ==================== 优先级指示器 ==================== */
.priority-high {
    -fx-background-color: #fecaca;
    -fx-text-fill: #dc2626;
    -fx-border-color: #ef4444;
}

.priority-medium {
    -fx-background-color: #fed7aa;
    -fx-text-fill: #ea580c;
    -fx-border-color: #f97316;
}

.priority-low {
    -fx-background-color: #dcfce7;
    -fx-text-fill: #16a34a;
    -fx-border-color: #22c55e;
}

/* ==================== 医学专业图标样式 ==================== */
.medical-icon {
    -fx-text-fill: #3b82f6;
    -fx-font-size: 16px;
}

/* ==================== 通用滚动条样式 ==================== */
.scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-pref-width: 10px;
    -fx-padding: 2;
}

.scroll-bar:vertical .track {
    -fx-background-color: #f1f5f9;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.scroll-bar:vertical .thumb {
    -fx-background-color: #cbd5e1;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.scroll-bar:vertical .thumb:hover {
    -fx-background-color: #94a3b8;
}

.scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #64748b;
}

/* ==================== 空列表提示样式 ==================== */
.list-view:empty {
    -fx-background-color: #f8fafc;
}

.list-view .placeholder .label {
    -fx-text-fill: #94a3b8;
    -fx-font-style: italic;
    -fx-font-size: 14px;
}
