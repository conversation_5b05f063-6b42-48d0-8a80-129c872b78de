.header-list {
    -fx-background-color: #ffffff;
    -fx-border-color: #e2e8f0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
}

.header-list-title {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e3a8a;
    -fx-padding: 16 16 12 16;
    -fx-background-color: #f8fafc;
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #e2e8f0;
}

.header-list-view {
    -fx-background-color: transparent;
    -fx-focus-color: transparent;
    -fx-padding: 4 0;
}

.header-list-view .list-cell {
    -fx-padding: 10px 16px;
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #f1f5f9;
    -fx-font-size: 14px;
    -fx-text-fill: #334155;
}

.header-list-view .list-cell:selected {
    -fx-background-color: #dbeafe;
    -fx-text-fill: #1e40af;
}

.header-list-view .list-cell:hover {
    -fx-background-color: #f8fafc;
}


