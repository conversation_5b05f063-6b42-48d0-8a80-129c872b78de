/* 医学研究管理系统 - HeaderList 组件样式 */
/* 基础标题列表组件的专用样式 */

/* ==================== 基础HeaderList样式 ==================== */
.header-list {
    -fx-background-color: #ffffff;
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1.5px;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.12), 8, 0, 0, 3);
    -fx-padding: 0;
}

.header-list-title {
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e3a8a;
    -fx-padding: 20 20 16 20;
    -fx-background-color: #f8fafc;
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #e2e8f0;
}

.header-list-view {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-padding: 4 0 4 0;
}

.header-list-view .list-cell {
    -fx-background-color: transparent;
    -fx-padding: 12px 16px;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #f1f5f9;
    -fx-font-size: 14px;
    -fx-text-fill: #334155;
    -fx-cursor: hand;
}

.header-list-view .list-cell:selected {
    -fx-background-color: #dbeafe;
    -fx-text-fill: #1e40af;
    -fx-border-color: #3b82f6;
    -fx-font-weight: 500;
}

.header-list-view .list-cell:hover {
    -fx-background-color: #f1f5f9;
    -fx-text-fill: #1e40af;
}

/* ==================== HeaderList专用滚动条 ==================== */
.header-list-view .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-pref-width: 10px;
    -fx-padding: 2;
}

.header-list-view .scroll-bar:vertical .track {
    -fx-background-color: #f1f5f9;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.header-list-view .scroll-bar:vertical .thumb {
    -fx-background-color: #cbd5e1;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.header-list-view .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #94a3b8;
}

.header-list-view .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #64748b;
}

/* ==================== HeaderList空列表样式 ==================== */
.header-list-view:empty {
    -fx-background-color: #f8fafc;
}

.header-list-view .placeholder .label {
    -fx-text-fill: #94a3b8;
    -fx-font-style: italic;
    -fx-font-size: 14px;
}
