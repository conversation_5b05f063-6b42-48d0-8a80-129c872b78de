/* 医学研究管理系统 - HeaderList 控件样式 */
/* 基于医学专业UI设计规范 */

/* 全局字体设置 */
.root {
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

/* 基础HeaderList样式 - 医学专业主题 */
.header-list {
    -fx-background-color: #ffffff;
    -fx-border-color: #d1d9e6;
    -fx-border-width: 1.5px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-padding: 0;
}

.header-list-title {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e3a8a;
    -fx-padding: 16 16 12 16;
    -fx-background-color: #f8fafc;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #e2e8f0;
}

.header-list-view {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-padding: 4 0 4 0;
}

.header-list-view .list-cell {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 12px 16px;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #f1f5f9;
    -fx-font-size: 14px;
    -fx-text-fill: #334155;
    -fx-cursor: hand;
}

.header-list-view .list-cell:selected {
    -fx-background-color: #dbeafe;
    -fx-text-fill: #1e40af;
    -fx-border-color: #3b82f6;
    -fx-font-weight: 500;
}

.header-list-view .list-cell:hover {
    -fx-background-color: #f1f5f9;
    -fx-text-fill: #1e40af;
}

/* 医学研究专用HeaderList样式 - 专业医学主题 */
.research-header-list {
    -fx-background-color: #ffffff;
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1.5px;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.12), 8, 0, 0, 3);
    -fx-padding: 0;
}

.research-header-title {
    -fx-font-size: 18px;
    -fx-font-weight: 700;
    -fx-text-fill: #0f172a;
    -fx-padding: 0 8 0 0;
}

.research-header-count {
    -fx-font-size: 14px;
    -fx-text-fill: #64748b;
    -fx-font-weight: 500;
    -fx-background-color: #f1f5f9;
    -fx-padding: 4 8 4 8;
    -fx-border-radius: 12px;
    -fx-background-radius: 12px;
    -fx-border-color: #e2e8f0;
    -fx-border-width: 1px;
}

.research-list-view {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-padding: 8 0 8 0;
}

.research-list-view .list-cell {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 0;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #f1f5f9;
    -fx-cursor: hand;
}

.research-list-view .list-cell:selected {
    -fx-background-color: #ecfdf5;
    -fx-border-color: #10b981;
    -fx-border-width: 0 0 0 4;
}

.research-list-view .list-cell:hover {
    -fx-background-color: #f8fafc;
}

/* 研究项目单元格样式 - 医学专业设计 */
.research-item-title {
    -fx-font-size: 15px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e293b;
    -fx-wrap-text: true;
    -fx-line-spacing: 2px;
}

.research-item-researcher {
    -fx-font-size: 13px;
    -fx-text-fill: #475569;
    -fx-font-weight: 500;
}

.research-item-status {
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-padding: 4 10 4 10;
    -fx-border-radius: 14px;
    -fx-background-radius: 14px;
    -fx-border-width: 1px;
}

.research-item-date {
    -fx-font-size: 12px;
    -fx-text-fill: #64748b;
    -fx-font-weight: 400;
}

/* 医学专业状态样式 */
.status-pending {
    -fx-background-color: #fef3c7;
    -fx-text-fill: #92400e;
    -fx-border-color: #f59e0b;
    -fx-effect: dropshadow(three-pass-box, rgba(245,158,11,0.2), 2, 0, 0, 1);
}

.status-approved {
    -fx-background-color: #d1fae5;
    -fx-text-fill: #065f46;
    -fx-border-color: #10b981;
    -fx-effect: dropshadow(three-pass-box, rgba(16,185,129,0.2), 2, 0, 0, 1);
}

.status-rejected {
    -fx-background-color: #fee2e2;
    -fx-text-fill: #991b1b;
    -fx-border-color: #ef4444;
    -fx-effect: dropshadow(three-pass-box, rgba(239,68,68,0.2), 2, 0, 0, 1);
}

/* 新增状态样式 */
.status-reviewing {
    -fx-background-color: #e0e7ff;
    -fx-text-fill: #3730a3;
    -fx-border-color: #6366f1;
    -fx-effect: dropshadow(three-pass-box, rgba(99,102,241,0.2), 2, 0, 0, 1);
}

.status-suspended {
    -fx-background-color: #f3f4f6;
    -fx-text-fill: #374151;
    -fx-border-color: #6b7280;
    -fx-effect: dropshadow(three-pass-box, rgba(107,114,128,0.2), 2, 0, 0, 1);
}

/* 专业滚动条样式 */
.header-list-view .scroll-bar:vertical,
.research-list-view .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-pref-width: 10px;
    -fx-padding: 2;
}

.header-list-view .scroll-bar:vertical .track,
.research-list-view .scroll-bar:vertical .track {
    -fx-background-color: #f1f5f9;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.header-list-view .scroll-bar:vertical .thumb,
.research-list-view .scroll-bar:vertical .thumb {
    -fx-background-color: #cbd5e1;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
}

.header-list-view .scroll-bar:vertical .thumb:hover,
.research-list-view .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #94a3b8;
}

.header-list-view .scroll-bar:vertical .thumb:pressed,
.research-list-view .scroll-bar:vertical .thumb:pressed {
    -fx-background-color: #64748b;
}

/* 空列表提示样式 */
.header-list-view:empty,
.research-list-view:empty {
    -fx-background-color: #f8fafc;
}

.header-list-view .placeholder .label,
.research-list-view .placeholder .label {
    -fx-text-fill: #94a3b8;
    -fx-font-style: italic;
    -fx-font-size: 14px;
}

/* 标题区域容器样式 */
.research-header-box {
    -fx-padding: 20 20 16 20;
    -fx-background-color: #f8fafc;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #e2e8f0;
}

/* 医学专业图标样式 */
.medical-icon {
    -fx-text-fill: #3b82f6;
    -fx-font-size: 16px;
}

/* 优先级指示器 */
.priority-high {
    -fx-background-color: #fecaca;
    -fx-text-fill: #dc2626;
    -fx-border-color: #ef4444;
}

.priority-medium {
    -fx-background-color: #fed7aa;
    -fx-text-fill: #ea580c;
    -fx-border-color: #f97316;
}

.priority-low {
    -fx-background-color: #dcfce7;
    -fx-text-fill: #16a34a;
    -fx-border-color: #22c55e;
}

/* ==================== FlatTable 扁平化表格样式 ==================== */

/* 扁平化表格面板 - 医学专业主题 */
.flat-table-panel {
    -fx-background-color: #ffffff;
    -fx-border-color: #cbd5e1;
    -fx-border-width: 1.5px;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.12), 8, 0, 0, 3);
    -fx-padding: 0;
}

/* 扁平化表格标题 */
.flat-table-title {
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #1e3a8a;
    -fx-background-color: #f8fafc;
    -fx-border-color: transparent transparent #e2e8f0 transparent;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 20 20 16 20;
}

/* 扁平化表格视图 */
.flat-table-view {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-table-cell-border-color: #e2e8f0;
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -fx-font-size: 14px;
}

/* 表格列标题 */
.flat-table-view .column-header {
    -fx-background-color: #f1f5f9;
    -fx-border-color: transparent transparent #cbd5e1 transparent;
    -fx-border-width: 0 0 2px 0;
    -fx-font-weight: 600;
    -fx-text-fill: #475569;
    -fx-font-size: 14px;
    -fx-padding: 12 16 12 16;
}

.flat-table-view .column-header .label {
    -fx-font-weight: 600;
    -fx-text-fill: #475569;
}

/* 表格行 */
.flat-table-row {
    -fx-background-color: transparent;
    -fx-border-color: transparent transparent #f1f5f9 transparent;
    -fx-border-width: 0 0 1px 0;
}

.flat-table-row:hover {
    -fx-background-color: #f8fafc;
}

.flat-table-row:selected {
    -fx-background-color: #e0e7ff;
    -fx-text-fill: #3730a3;
}

/* 表格单元格 */
.flat-table-view .table-cell {
    -fx-border-color: transparent;
    -fx-padding: 12 16 12 16;
    -fx-text-fill: #374151;
    -fx-font-size: 14px;
    -fx-alignment: CENTER-LEFT;
}

/* 序号列样式 */
.flat-table-column-number .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 500;
    -fx-text-fill: #6b7280;
    -fx-font-size: 13px;
}

/* 名称列样式 */
.flat-table-column-name .table-cell {
    -fx-alignment: CENTER-LEFT;
    -fx-font-weight: 500;
    -fx-text-fill: #1f2937;
}

/* 权重列样式 */
.flat-table-column-weight .table-cell {
    -fx-alignment: CENTER;
    -fx-font-family: "Consolas", "Monaco", monospace;
    -fx-text-fill: #059669;
    -fx-font-weight: 500;
}

/* 符号列样式 */
.flat-table-column-symbol .table-cell {
    -fx-alignment: CENTER;
    -fx-font-weight: 600;
    -fx-text-fill: #7c3aed;
    -fx-font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .research-header-title {
        -fx-font-size: 16px;
    }

    .research-item-title {
        -fx-font-size: 14px;
    }

    .flat-table-panel {
        -fx-min-width: 400px;
    }

    .flat-table-view .table-cell {
        -fx-font-size: 13px;
        -fx-padding: 10 12 10 12;
    }
}
