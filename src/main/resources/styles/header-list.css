/* HeaderList 控件样式 */

/* 基础HeaderList样式 */
.header-list {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
}

.header-list-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-padding: 0 0 5 0;
}

.header-list-view {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
}

.header-list-view .list-cell {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 8px 12px;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #f0f0f0;
}

.header-list-view .list-cell:selected {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
}

.header-list-view .list-cell:hover {
    -fx-background-color: #f5f5f5;
}

/* 医学研究专用HeaderList样式 */
.research-header-list {
    -fx-background-color: #fafafa;
    -fx-border-color: #d0d0d0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.research-header-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #1a237e;
}

.research-header-count {
    -fx-font-size: 14px;
    -fx-text-fill: #666666;
    -fx-font-style: italic;
}

.research-list-view {
    -fx-background-color: #ffffff;
    -fx-border-color: #e0e0e0;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.research-list-view .list-cell {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 0;
    -fx-border-width: 0 0 1 0;
    -fx-border-color: #eeeeee;
}

.research-list-view .list-cell:selected {
    -fx-background-color: #e8f5e8;
    -fx-border-color: #4caf50;
}

.research-list-view .list-cell:hover {
    -fx-background-color: #f9f9f9;
}

/* 研究项目单元格样式 */
.research-item-title {
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-wrap-text: true;
}

.research-item-researcher {
    -fx-font-size: 12px;
    -fx-text-fill: #34495e;
}

.research-item-status {
    -fx-font-size: 12px;
    -fx-padding: 2 6 2 6;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
}

.research-item-date {
    -fx-font-size: 12px;
    -fx-text-fill: #7f8c8d;
}

/* 状态样式 */
.status-pending {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1px;
}

.status-approved {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
    -fx-border-width: 1px;
}

.status-rejected {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
    -fx-border-width: 1px;
}

/* 滚动条样式 */
.research-list-view .scroll-bar:vertical {
    -fx-background-color: transparent;
    -fx-pref-width: 8px;
}

.research-list-view .scroll-bar:vertical .track {
    -fx-background-color: #f0f0f0;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.research-list-view .scroll-bar:vertical .thumb {
    -fx-background-color: #c0c0c0;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.research-list-view .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #a0a0a0;
}

/* 空列表提示 */
.research-list-view:empty {
    -fx-background-color: #f9f9f9;
}

.research-list-view .placeholder .label {
    -fx-text-fill: #999999;
    -fx-font-style: italic;
}
