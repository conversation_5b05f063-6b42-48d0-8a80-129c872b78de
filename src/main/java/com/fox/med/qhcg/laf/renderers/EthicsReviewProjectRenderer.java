package com.fox.med.qhcg.laf.renderers;

import com.fox.med.qhcg.model.EthicsReviewProject;
import com.fox.med.qhcg.model.ReviewState;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;

/**
 * 医学研究伦理审查项目的自定义列表渲染器
 * 专门用于显示EthicsReviewProject对象的列表单元格
 * 采用医学专业设计，支持多行信息显示和状态可视化
 */
public class EthicsReviewProjectRenderer extends ListCell<EthicsReviewProject> {
    
    private final VBox containerBox;
    private final Label projectTitleLabel;
    private final Label researcherLabel;
    private final Label reviewStatusLabel;
    private final Label submissionDateLabel;
    private final HBox detailsBox;
    
    public EthicsReviewProjectRenderer() {
        // 创建项目标题标签
        projectTitleLabel = new Label();
        projectTitleLabel.getStyleClass().add("research-item-title");
        projectTitleLabel.setWrapText(true);
        projectTitleLabel.setMaxWidth(Double.MAX_VALUE);
        
        // 创建研究者标签
        researcherLabel = new Label();
        researcherLabel.getStyleClass().add("research-item-researcher");
        
        // 创建审查状态标签
        reviewStatusLabel = new Label();
        reviewStatusLabel.getStyleClass().add("research-item-status");
        
        // 创建提交日期标签
        submissionDateLabel = new Label();
        submissionDateLabel.getStyleClass().add("research-item-date");
        
        // 底部详情信息容器 - 改进布局
        detailsBox = new HBox(12);
        detailsBox.setAlignment(Pos.CENTER_LEFT);
        detailsBox.getChildren().addAll(researcherLabel, reviewStatusLabel, submissionDateLabel);
        
        // 主容器 - 医学专业间距
        containerBox = new VBox(8);
        containerBox.setPadding(new Insets(16, 20, 16, 20));
        containerBox.getChildren().addAll(projectTitleLabel, detailsBox);
        
        // 设置容器拉伸
        VBox.setVgrow(projectTitleLabel, Priority.NEVER);
        HBox.setHgrow(researcherLabel, Priority.ALWAYS);
        
        // 设置容器样式
        containerBox.setMaxWidth(Double.MAX_VALUE);
    }
    
    @Override
    protected void updateItem(EthicsReviewProject project, boolean empty) {
        super.updateItem(project, empty);
        
        if (empty || project == null) {
            setGraphic(null);
            setText(null);
        } else {
            // 设置项目标题
            projectTitleLabel.setText(project.getTitle());
            
            // 设置研究者信息
            if (project.getResearcher() != null && !project.getResearcher().isEmpty()) {
                researcherLabel.setText("研究者: " + project.getResearcher());
                researcherLabel.setVisible(true);
                researcherLabel.setManaged(true);
            } else {
                researcherLabel.setVisible(false);
                researcherLabel.setManaged(false);
            }
            
            // 设置审查状态信息 - 使用ReviewState枚举简化处理
            ReviewState reviewState = project.getReviewState();
            if (reviewState != null) {
                reviewStatusLabel.setText(reviewState.getDisplayName());
                reviewStatusLabel.setVisible(true);
                reviewStatusLabel.setManaged(true);

                // 清除所有状态样式
                reviewStatusLabel.getStyleClass().removeAll(
                    "status-pending", "status-approved", "status-rejected",
                    "status-reviewing", "status-suspended"
                );

                // 使用ReviewState的CSS类名
                reviewStatusLabel.getStyleClass().add(reviewState.getCssClass());
            } else {
                reviewStatusLabel.setVisible(false);
                reviewStatusLabel.setManaged(false);
            }
            
            // 设置提交日期信息
            if (project.getDate() != null && !project.getDate().isEmpty()) {
                submissionDateLabel.setText("提交日期: " + project.getDate());
                submissionDateLabel.setVisible(true);
                submissionDateLabel.setManaged(true);
            } else {
                submissionDateLabel.setVisible(false);
                submissionDateLabel.setManaged(false);
            }
            
            setGraphic(containerBox);
            setText(null);
        }
    }
}
