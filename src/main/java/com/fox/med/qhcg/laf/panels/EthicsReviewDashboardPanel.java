package com.fox.med.qhcg.laf.panels;

import com.fox.med.qhcg.model.EthicsReviewProject;
import com.fox.med.qhcg.laf.components.HeaderList;
import com.fox.med.qhcg.laf.components.EthicsReviewList;
import com.fox.med.qhcg.laf.renderers.EthicsReviewProjectRenderer;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.Separator;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;

/**
 * 医学研究伦理审查主仪表板面板
 * 展示多个列表控件的综合使用，包括待审核、已通过、已拒绝项目等
 */
public class EthicsReviewDashboardPanel extends VBox {
    
    private EthicsReviewListPanel<EthicsReviewProject> pendingProjectsPanel;
    private EthicsReviewListPanel<EthicsReviewProject> approvedProjectsPanel;
    private EthicsReviewListPanel<EthicsReviewProject> rejectedProjectsPanel;
    private HeaderList departmentListPanel;
    private HeaderList researcherListPanel;
    
    public EthicsReviewDashboardPanel() {
        initializeComponents();
        setupLayout();
        loadSampleData();
    }
    
    private void initializeComponents() {
        // 创建各种列表控件
        pendingProjectsPanel = new EthicsReviewListPanel<>("待审核项目");
        pendingProjectsPanel.setCellFactory(listView -> new EthicsReviewProjectRenderer());
        
        approvedProjectsPanel = new EthicsReviewListPanel<>("已通过项目");
        approvedProjectsPanel.setCellFactory(listView -> new EthicsReviewProjectRenderer());
        
        rejectedProjectsPanel = new EthicsReviewListPanel<>("已拒绝项目");
        rejectedProjectsPanel.setCellFactory(listView -> new EthicsReviewProjectRenderer());
        
        departmentListPanel = new HeaderList("参与科室");
        researcherListPanel = new HeaderList("研究人员");
    }
    
    private void setupLayout() {
        this.setSpacing(20);
        this.setPadding(new Insets(20));
        
        // 标题区域
        Label titleLabel = new Label("医学研究伦理审查管理系统");
        titleLabel.setStyle("-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1a237e;");
        
        // 统计信息区域
        HBox statisticsBox = createStatisticsBox();
        
        // 分隔线
        Separator separator = new Separator();
        
        // 主内容区域
        GridPane contentGrid = new GridPane();
        contentGrid.setHgap(20);
        contentGrid.setVgap(20);
        
        // 设置列约束
        contentGrid.getColumnConstraints().addAll(
            new javafx.scene.layout.ColumnConstraints(300), // 左侧列
            new javafx.scene.layout.ColumnConstraints(400), // 中间列
            new javafx.scene.layout.ColumnConstraints(300)  // 右侧列
        );
        
        // 设置行约束
        contentGrid.getRowConstraints().addAll(
            new javafx.scene.layout.RowConstraints(350), // 第一行
            new javafx.scene.layout.RowConstraints(350)  // 第二行
        );
        
        // 添加控件到网格
        contentGrid.add(pendingProjectsPanel, 0, 0);
        contentGrid.add(approvedProjectsPanel, 1, 0);
        contentGrid.add(rejectedProjectsPanel, 2, 0);
        contentGrid.add(departmentListPanel, 0, 1);
        contentGrid.add(researcherListPanel, 1, 1);
        
        // 操作按钮区域
        HBox actionButtonsBox = createActionButtonsBox();
        
        // 添加到主布局
        this.getChildren().addAll(
            titleLabel,
            statisticsBox,
            separator,
            contentGrid,
            actionButtonsBox
        );
        
        // 设置拉伸
        VBox.setVgrow(contentGrid, Priority.ALWAYS);
    }
    
    private HBox createStatisticsBox() {
        HBox statisticsBox = new HBox(30);
        statisticsBox.setAlignment(Pos.CENTER);
        statisticsBox.setPadding(new Insets(15));
        statisticsBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-radius: 8px; -fx-background-radius: 8px;");
        
        // 创建统计卡片
        VBox totalCard = createStatisticsCard("总项目数", "0", "#3498db");
        VBox pendingCard = createStatisticsCard("待审核", "0", "#f39c12");
        VBox approvedCard = createStatisticsCard("已通过", "0", "#27ae60");
        VBox rejectedCard = createStatisticsCard("已拒绝", "0", "#e74c3c");
        
        statisticsBox.getChildren().addAll(totalCard, pendingCard, approvedCard, rejectedCard);
        
        return statisticsBox;
    }
    
    private VBox createStatisticsCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.setStyle("-fx-background-color: white; -fx-border-radius: 6px; -fx-background-radius: 6px; " +
                     "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 3, 0, 0, 1);");
        
        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #666666;");
        
        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");
        
        card.getChildren().addAll(valueLabel, titleLabel);
        
        return card;
    }
    
    private HBox createActionButtonsBox() {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(15, 0, 0, 0));
        
        Button addProjectButton = new Button("新增项目");
        addProjectButton.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 14px; " +
                                 "-fx-padding: 10 20 10 20; -fx-border-radius: 5px; -fx-background-radius: 5px;");
        
        Button refreshDataButton = new Button("刷新数据");
        refreshDataButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 14px; " +
                                  "-fx-padding: 10 20 10 20; -fx-border-radius: 5px; -fx-background-radius: 5px;");
        
        Button exportReportButton = new Button("导出报告");
        exportReportButton.setStyle("-fx-background-color: #9b59b6; -fx-text-fill: white; -fx-font-size: 14px; " +
                                   "-fx-padding: 10 20 10 20; -fx-border-radius: 5px; -fx-background-radius: 5px;");
        
        buttonBox.getChildren().addAll(addProjectButton, refreshDataButton, exportReportButton);
        
        return buttonBox;
    }
    
    private void loadSampleData() {
        // 加载待审核项目 - 使用更真实的医学研究数据
        pendingProjectsPanel.addItems(
            new EthicsReviewProject(
                "新型冠状病毒疫苗安全性及免疫原性临床研究", 
                "张志强 主任医师", 
                "待审核", 
                "2024-01-25", 
                "感染科",
                "高",
                "BTCH-2024-001"
            ),

            new EthicsReviewProject(
                "儿童支气管哮喘雾化吸入治疗新方法临床验证", 
                "王小红 副主任医师", 
                "待提交", 
                "2024-01-23", 
                "儿科",
                "中",
                ""
            )
        );
        
        // 加载已通过项目
        approvedProjectsPanel.addItems(
            new EthicsReviewProject(
                "2型糖尿病肾病早期诊断标志物及干预策略研究", 
                "赵建国 主任医师", 
                "已通过", 
                "2024-01-20", 
                "内分泌科",
                "高",
                "BTCH-2023-089"
            ),
            new EthicsReviewProject(
                "阿尔茨海默病早期认知功能干预治疗效果评估", 
                "陈丽娟 教授", 
                "已通过", 
                "2024-01-18", 
                "神经内科",
                "高",
                "BTCH-2023-087"
            )
        );
        
        // 加载已拒绝项目
        rejectedProjectsPanel.addItems(
            new EthicsReviewProject(
                "实验性抗肿瘤药物PD-1抑制剂临床试验", 
                "刘永强 副教授", 
                "已拒绝", 
                "2024-01-15", 
                "肿瘤科",
                "高",
                "BTCH-2024-003"
            )
        );
        
        // 加载科室列表
        departmentListPanel.addItems(
            "心血管内科", "神经内科", "呼吸与危重症医学科", "消化内科", 
            "内分泌代谢科", "肾脏内科", "血液科", "肿瘤内科", 
            "感染科", "儿科", "妇产科", "骨科"
        );
        
        // 加载研究人员列表
        researcherListPanel.addItems(
            "张志强 主任医师", "李明华 教授", "王小红 副主任医师", "赵建国 主任医师", 
            "陈丽娟 教授", "刘永强 副教授", "周建华 副主任医师", "吴教授"
        );
    }
}
