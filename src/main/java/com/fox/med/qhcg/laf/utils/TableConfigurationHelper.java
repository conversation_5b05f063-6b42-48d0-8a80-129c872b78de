package com.fox.med.qhcg.laf.utils;

import com.fox.med.qhcg.model.TableDataItem;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.PropertyValueFactory;

/**
 * 表格配置助手类
 * 用于减少FlatTable和FlatTablePanel中的重复代码
 */
public class TableConfigurationHelper {
    
    /**
     * 配置表格列
     */
    public static void configureTableColumns(
            TableColumn<TableDataItem, String> numberColumn,
            TableColumn<TableDataItem, String> nameColumn,
            TableColumn<TableDataItem, String> weightColumn,
            TableColumn<TableDataItem, String> symbolColumn,
            TableView<TableDataItem> tableView) {
        
        // 设置列值工厂
        numberColumn.setCellValueFactory(new PropertyValueFactory<>("number"));
        nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        weightColumn.setCellValueFactory(new PropertyValueFactory<>("weight"));
        symbolColumn.setCellValueFactory(new PropertyValueFactory<>("symbol"));
        
        // 设置列样式类
        numberColumn.getStyleClass().add("flat-column-number");
        nameColumn.getStyleClass().add("flat-column-name");
        weightColumn.getStyleClass().add("flat-column-weight");
        symbolColumn.getStyleClass().add("flat-column-symbol");
        
        // 设置列宽比例
        numberColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.1)); // 10%
        nameColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.4));   // 40%
        weightColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.3)); // 30%
        symbolColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.2)); // 20%
        
        // 禁止调整列宽
        numberColumn.setResizable(false);
        nameColumn.setResizable(false);
        weightColumn.setResizable(false);
        symbolColumn.setResizable(false);
        
        // 设置列对齐
        numberColumn.setStyle("-fx-alignment: CENTER;");
        nameColumn.setStyle("-fx-alignment: CENTER-LEFT;");
        weightColumn.setStyle("-fx-alignment: CENTER;");
        symbolColumn.setStyle("-fx-alignment: CENTER;");
        
        // 禁用表格标题排序
        numberColumn.setSortable(false);
        nameColumn.setSortable(false);
        weightColumn.setSortable(false);
        symbolColumn.setSortable(false);
    }
    
    /**
     * 配置表格基本属性
     */
    public static void configureTableView(TableView<TableDataItem> tableView, String rowStyleClass) {
        // 设置列调整策略
        tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY_FLEX_LAST_COLUMN);
        
        // 设置行工厂
        tableView.setRowFactory(tv -> {
            TableRow<TableDataItem> row = new TableRow<>();
            row.getStyleClass().add(rowStyleClass);
            return row;
        });
    }
    
    /**
     * 创建标准的表格列
     */
    public static TableColumn<TableDataItem, String> createNumberColumn() {
        return new TableColumn<>("No.");
    }
    
    public static TableColumn<TableDataItem, String> createNameColumn() {
        return new TableColumn<>("Name");
    }
    
    public static TableColumn<TableDataItem, String> createWeightColumn() {
        return new TableColumn<>("Weight");
    }
    
    public static TableColumn<TableDataItem, String> createSymbolColumn() {
        return new TableColumn<>("Symbol");
    }
}
