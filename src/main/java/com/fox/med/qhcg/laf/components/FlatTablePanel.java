package com.fox.med.qhcg.laf.components;

import com.fox.med.qhcg.model.TableDataItem;
import com.fox.med.qhcg.laf.utils.TableConfigurationHelper;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.scene.layout.Priority;

/**
 * 扁平化表格面板组件
 * 基于医学专业主题设计的现代化表格控件
 * 支持清晰的视觉层次和简洁的数据展示
 */
public class FlatTablePanel extends VBox {
    
    private final StringProperty titleText = new SimpleStringProperty();
    private final Label titleLabel;
    private final TableView<TableDataItem> tableView;
    private final ObservableList<TableDataItem> items;
    
    // 表格列
    private final TableColumn<TableDataItem, String> numberColumn;
    private final TableColumn<TableDataItem, String> nameColumn;
    private final TableColumn<TableDataItem, String> weightColumn;
    private final TableColumn<TableDataItem, String> symbolColumn;
    
    public FlatTablePanel() {
        this("数据表格");
    }
    
    public FlatTablePanel(String titleText) {
        this.items = FXCollections.observableArrayList();
        
        // 创建标题标签
        this.titleLabel = new Label();
        this.titleLabel.getStyleClass().add("flat-table-title");
        this.titleLabel.textProperty().bind(this.titleText);
        
        // 创建表格视图
        this.tableView = new TableView<>(items);
        this.tableView.getStyleClass().add("flat-table-view");
        
        // 创建表格列
        this.numberColumn = TableConfigurationHelper.createNumberColumn();
        this.nameColumn = TableConfigurationHelper.createNameColumn();
        this.weightColumn = TableConfigurationHelper.createWeightColumn();
        this.symbolColumn = TableConfigurationHelper.createSymbolColumn();
        
        // 添加列到表格
        tableView.getColumns().addAll(numberColumn, nameColumn, weightColumn, symbolColumn);
        
        // 设置列宽和样式
        setupColumnWidths();
        
        // 设置布局
        setupLayout();
        
        // 设置初始标题
        setTitleText(titleText);
        
        // 应用样式类
        this.getStyleClass().add("flat-table-panel");
    }
    
    private void setupColumnWidths() {
        // 使用助手类配置表格列
        TableConfigurationHelper.configureTableColumns(
            numberColumn, nameColumn, weightColumn, symbolColumn, tableView);
    }
    
    private void setupLayout() {
        // 设置间距和内边距 - 医学专业布局
        this.setSpacing(0);
        this.setPadding(new Insets(0));
        this.setAlignment(Pos.TOP_LEFT);
        
        // 设置标题样式
        this.titleLabel.setPadding(new Insets(20, 20, 16, 20));
        
        // 标题不拉伸，表格拉伸填充剩余空间
        VBox.setVgrow(titleLabel, Priority.NEVER);
        VBox.setVgrow(tableView, Priority.ALWAYS);
        
        // 添加子控件
        this.getChildren().addAll(titleLabel, tableView);
        
        // 使用助手类配置表格视图
        TableConfigurationHelper.configureTableView(tableView, "flat-table-row");
    }
    
    // 标题文本属性
    public StringProperty titleTextProperty() {
        return titleText;
    }
    
    public String getTitleText() {
        return titleText.get();
    }
    
    public void setTitleText(String titleText) {
        this.titleText.set(titleText);
    }
    
    // 数据操作
    public ObservableList<TableDataItem> getItems() {
        return items;
    }
    
    public void addItem(TableDataItem item) {
        items.add(item);
    }
    
    @SafeVarargs
    public final void addItems(TableDataItem... items) {
        this.items.addAll(items);
    }
    
    public void removeItem(TableDataItem item) {
        items.remove(item);
    }
    
    public void clearItems() {
        items.clear();
    }
    
    // 获取TableView以便进行更高级的定制
    public TableView<TableDataItem> getTableView() {
        return tableView;
    }
    
    // 获取标题Label
    public Label getTitleLabel() {
        return titleLabel;
    }
    
    // 获取各列
    public TableColumn<TableDataItem, String> getNumberColumn() {
        return numberColumn;
    }
    
    public TableColumn<TableDataItem, String> getNameColumn() {
        return nameColumn;
    }
    
    public TableColumn<TableDataItem, String> getWeightColumn() {
        return weightColumn;
    }
    
    public TableColumn<TableDataItem, String> getSymbolColumn() {
        return symbolColumn;
    }
    
    /**
     * 设置表格选择模式
     */
    public void setSelectionMode(SelectionMode mode) {
        tableView.getSelectionModel().setSelectionMode(mode);
    }
    
    /**
     * 获取选中的项目
     */
    public TableDataItem getSelectedItem() {
        return tableView.getSelectionModel().getSelectedItem();
    }
    
    /**
     * 获取选中项目的索引
     */
    public int getSelectedIndex() {
        return tableView.getSelectionModel().getSelectedIndex();
    }
    
    /**
     * 设置是否显示表格标题
     */
    public void setShowTitle(boolean show) {
        titleLabel.setVisible(show);
        titleLabel.setManaged(show);
    }
}
