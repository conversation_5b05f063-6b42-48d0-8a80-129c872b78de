package com.fox.med.qhcg.laf.components;

import com.fox.med.qhcg.model.TableDataItem;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.VBox;

/**
 * 扁平化表格组件
 * 简洁的表格控件，基于医学专业主题设计
 */
public class FlatTable extends VBox {
    
    private final TableView<TableDataItem> tableView;
    private final ObservableList<TableDataItem> items;
    
    // 表格列
    private final TableColumn<TableDataItem, String> numberColumn;
    private final TableColumn<TableDataItem, String> nameColumn;
    private final TableColumn<TableDataItem, String> weightColumn;
    private final TableColumn<TableDataItem, String> symbolColumn;
    
    public FlatTable() {
        this.items = FXCollections.observableArrayList();

        // 创建表格视图
        this.tableView = new TableView<>(items);
        this.tableView.getStyleClass().add("flat-table");
        
        // 创建表格列
        this.numberColumn = new TableColumn<>("No.");
        this.numberColumn.setCellValueFactory(new PropertyValueFactory<>("number"));
        this.numberColumn.getStyleClass().add("flat-column-number");

        this.nameColumn = new TableColumn<>("Name");
        this.nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        this.nameColumn.getStyleClass().add("flat-column-name");

        this.weightColumn = new TableColumn<>("Weight");
        this.weightColumn.setCellValueFactory(new PropertyValueFactory<>("weight"));
        this.weightColumn.getStyleClass().add("flat-column-weight");

        this.symbolColumn = new TableColumn<>("Symbol");
        this.symbolColumn.setCellValueFactory(new PropertyValueFactory<>("symbol"));
        this.symbolColumn.getStyleClass().add("flat-column-symbol");
        
        // 添加列到表格
        tableView.getColumns().addAll(numberColumn, nameColumn, weightColumn, symbolColumn);
        
        // 设置列宽和样式
        setupTableProperties();
        
        // 设置布局
        setupLayout();
        
        // 应用样式类
        this.getStyleClass().add("flat-table-container");
    }
    
    private void setupTableProperties() {
        // 设置列宽比例 - 基于图片中的视觉比例
        numberColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.1)); // 10%
        nameColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.4));   // 40%
        weightColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.3)); // 30%
        symbolColumn.prefWidthProperty().bind(tableView.widthProperty().multiply(0.2)); // 20%
        
        // 禁止调整列宽
        numberColumn.setResizable(false);
        nameColumn.setResizable(false);
        weightColumn.setResizable(false);
        symbolColumn.setResizable(false);
        
        // 设置列对齐
        numberColumn.setStyle("-fx-alignment: CENTER;");
        nameColumn.setStyle("-fx-alignment: CENTER-LEFT;");
        weightColumn.setStyle("-fx-alignment: CENTER;");
        symbolColumn.setStyle("-fx-alignment: CENTER;");
        
        // 表格设置
        tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
        tableView.setRowFactory(tv -> {
            TableRow<TableDataItem> row = new TableRow<>();
            row.getStyleClass().add("flat-table-row");
            return row;
        });

        // 禁用表格标题排序
        numberColumn.setSortable(false);
        nameColumn.setSortable(false);
        weightColumn.setSortable(false);
        symbolColumn.setSortable(false);
    }

    private void setupLayout() {
        // 设置间距和内边距
        this.setSpacing(0);
        this.setPadding(new Insets(0));

        // 添加表格到容器
        this.getChildren().add(tableView);

        // 设置表格填充整个容器
        VBox.setVgrow(tableView, javafx.scene.layout.Priority.ALWAYS);
    }
    
    // 数据操作方法
    public ObservableList<TableDataItem> getItems() {
        return items;
    }
    
    public void addItem(TableDataItem item) {
        items.add(item);
    }
    
    public void addItems(TableDataItem... items) {
        this.items.addAll(items);
    }
    
    public void removeItem(TableDataItem item) {
        items.remove(item);
    }
    
    public void clearItems() {
        items.clear();
    }
    
    // 获取TableView以便进行更高级的定制
    public TableView<TableDataItem> getTableView() {
        return tableView;
    }
    
    // 获取各列
    public TableColumn<TableDataItem, String> getNumberColumn() {
        return numberColumn;
    }
    
    public TableColumn<TableDataItem, String> getNameColumn() {
        return nameColumn;
    }
    
    public TableColumn<TableDataItem, String> getWeightColumn() {
        return weightColumn;
    }
    
    public TableColumn<TableDataItem, String> getSymbolColumn() {
        return symbolColumn;
    }
    
    /**
     * 设置表格选择模式
     */
    public void setSelectionMode(SelectionMode mode) {
        tableView.getSelectionModel().setSelectionMode(mode);
    }
    
    /**
     * 获取选中的项目
     */
    public TableDataItem getSelectedItem() {
        return tableView.getSelectionModel().getSelectedItem();
    }
    
    /**
     * 获取选中项目的索引
     */
    public int getSelectedIndex() {
        return tableView.getSelectionModel().getSelectedIndex();
    }
    
    /**
     * 设置表格高度
     */
    public void setTableHeight(double height) {
        tableView.setPrefHeight(height);
        tableView.setMinHeight(height);
        tableView.setMaxHeight(height);
    }
    
    /**
     * 设置表格最小高度
     */
    public void setMinTableHeight(double height) {
        tableView.setMinHeight(height);
    }
    
    /**
     * 设置表格最大高度
     */
    public void setMaxTableHeight(double height) {
        tableView.setMaxHeight(height);
    }
}
