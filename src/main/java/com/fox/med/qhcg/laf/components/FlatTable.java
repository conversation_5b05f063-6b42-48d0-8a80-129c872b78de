package com.fox.med.qhcg.laf.components;

import com.fox.med.qhcg.model.TableDataItem;
import com.fox.med.qhcg.laf.utils.TableConfigurationHelper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;

/**
 * 扁平化表格组件
 * 简洁的表格控件，基于医学专业主题设计
 */
public class FlatTable extends VBox {
    
    private final TableView<TableDataItem> tableView;
    private final ObservableList<TableDataItem> items;
    
    // 表格列
    private final TableColumn<TableDataItem, String> numberColumn;
    private final TableColumn<TableDataItem, String> nameColumn;
    private final TableColumn<TableDataItem, String> weightColumn;
    private final TableColumn<TableDataItem, String> symbolColumn;
    
    public FlatTable() {
        this.items = FXCollections.observableArrayList();

        // 创建表格视图
        this.tableView = new TableView<>(items);
        this.tableView.getStyleClass().add("flat-table");
        
        // 创建表格列
        this.numberColumn = TableConfigurationHelper.createNumberColumn();
        this.nameColumn = TableConfigurationHelper.createNameColumn();
        this.weightColumn = TableConfigurationHelper.createWeightColumn();
        this.symbolColumn = TableConfigurationHelper.createSymbolColumn();
        
        // 添加列到表格
        tableView.getColumns().addAll(numberColumn, nameColumn, weightColumn, symbolColumn);
        
        // 设置列宽和样式
        setupTableProperties();
        
        // 设置布局
        setupLayout();
        
        // 应用样式类
        this.getStyleClass().add("flat-table-container");
    }
    
    private void setupTableProperties() {
        // 使用助手类配置表格列
        TableConfigurationHelper.configureTableColumns(
            numberColumn, nameColumn, weightColumn, symbolColumn, tableView);

        // 使用助手类配置表格视图
        TableConfigurationHelper.configureTableView(tableView, "flat-table-row");
    }

    private void setupLayout() {
        // 设置间距和内边距
        this.setSpacing(0);
        this.setPadding(new Insets(0));

        // 添加表格到容器
        this.getChildren().add(tableView);

        // 设置表格填充整个容器
        VBox.setVgrow(tableView, javafx.scene.layout.Priority.ALWAYS);
    }
    
    // 数据操作方法
    public ObservableList<TableDataItem> getItems() {
        return items;
    }
    
    public void addItem(TableDataItem item) {
        items.add(item);
    }
    
    @SafeVarargs
    public final void addItems(TableDataItem... items) {
        this.items.addAll(items);
    }
    
    public void removeItem(TableDataItem item) {
        items.remove(item);
    }
    
    public void clearItems() {
        items.clear();
    }
    
    // 获取TableView以便进行更高级的定制
    public TableView<TableDataItem> getTableView() {
        return tableView;
    }
    
    // 获取各列
    public TableColumn<TableDataItem, String> getNumberColumn() {
        return numberColumn;
    }
    
    public TableColumn<TableDataItem, String> getNameColumn() {
        return nameColumn;
    }
    
    public TableColumn<TableDataItem, String> getWeightColumn() {
        return weightColumn;
    }
    
    public TableColumn<TableDataItem, String> getSymbolColumn() {
        return symbolColumn;
    }
    
    /**
     * 设置表格选择模式
     */
    public void setSelectionMode(SelectionMode mode) {
        tableView.getSelectionModel().setSelectionMode(mode);
    }
    
    /**
     * 获取选中的项目
     */
    public TableDataItem getSelectedItem() {
        return tableView.getSelectionModel().getSelectedItem();
    }
    
    /**
     * 获取选中项目的索引
     */
    public int getSelectedIndex() {
        return tableView.getSelectionModel().getSelectedIndex();
    }
    
    /**
     * 设置表格高度
     */
    public void setTableHeight(double height) {
        tableView.setPrefHeight(height);
        tableView.setMinHeight(height);
        tableView.setMaxHeight(height);
    }
    
    /**
     * 设置表格最小高度
     */
    public void setMinTableHeight(double height) {
        tableView.setMinHeight(height);
    }
    
    /**
     * 设置表格最大高度
     */
    public void setMaxTableHeight(double height) {
        tableView.setMaxHeight(height);
    }
}
