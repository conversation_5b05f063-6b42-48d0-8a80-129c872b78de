package com.fox.med.qhcg.laf.components;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.util.Callback;

/**
 * 医学研究伦理审查列表面板
 * 专为医学研究项目设计的增强版列表控件
 * 支持泛型数据模型和更丰富地显示格式
 */
public class EthicsReviewListPanel<T> extends VBox {
    
    private final StringProperty titleText = new SimpleStringProperty();
    private final Label titleLabel;
    private final Label itemCountLabel;
    private final ListView<T> listView;
    private final ObservableList<T> items;
    private final HBox titleContainer;
    
    public EthicsReviewListPanel() {
        this("");
    }
    
    public EthicsReviewListPanel(String titleText) {
        this.items = FXCollections.observableArrayList();
        
        // 创建标题区域
        this.titleLabel = new Label();
        this.titleLabel.getStyleClass().add("research-header-title");
        this.titleLabel.textProperty().bind(this.titleText);
        
        // 创建计数标签
        this.itemCountLabel = new Label("(0)");
        this.itemCountLabel.getStyleClass().add("research-header-count");
        
        // 标题容器
        this.titleContainer = new HBox(10);
        this.titleContainer.setAlignment(Pos.CENTER_LEFT);
        this.titleContainer.getChildren().addAll(titleLabel, itemCountLabel);
        
        // 创建列表视图
        this.listView = new ListView<>(items);
        this.listView.getStyleClass().add("research-list-view");
        
        // 监听列表项数量变化
        items.addListener((javafx.collections.ListChangeListener<T>) change -> {
            updateItemCountLabel();
        });
        
        // 设置布局
        setupLayout();
        
        // 设置初始标题
        setTitleText(titleText);
        
        // 应用样式类
        this.getStyleClass().add("research-header-list");
    }
    
    private void setupLayout() {
        // 设置间距和内边距 - 医学专业布局
        this.setSpacing(0);
        this.setPadding(new Insets(0));
        this.setAlignment(Pos.TOP_LEFT);
        
        // 设置标题区域样式
        this.titleContainer.getStyleClass().add("research-header-box");
        this.titleContainer.setPadding(new Insets(20, 20, 16, 20));
        
        // 标题不拉伸，列表拉伸填充剩余空间
        VBox.setVgrow(titleContainer, Priority.NEVER);
        VBox.setVgrow(listView, Priority.ALWAYS);
        
        // 添加子控件
        this.getChildren().addAll(titleContainer, listView);
    }
    
    private void updateItemCountLabel() {
        itemCountLabel.setText("(" + items.size() + ")");
    }
    
    // 标题文本属性
    public StringProperty titleTextProperty() {
        return titleText;
    }
    
    public String getTitleText() {
        return titleText.get();
    }
    
    public void setTitleText(String titleText) {
        this.titleText.set(titleText);
    }
    
    // 列表项操作
    public ObservableList<T> getItems() {
        return items;
    }
    
    public void addItem(T item) {
        items.add(item);
    }
    
    @SafeVarargs
    public final void addItems(T... items) {
        this.items.addAll(items);
    }
    
    public void removeItem(T item) {
        items.remove(item);
    }
    
    public void clearItems() {
        items.clear();
    }
    
    // 设置自定义单元格工厂
    public void setCellFactory(Callback<ListView<T>, ListCell<T>> cellFactory) {
        listView.setCellFactory(cellFactory);
    }
    
    // 获取ListView以便进行更高级的定制
    public ListView<T> getListView() {
        return listView;
    }
    
    // 获取标题Label以便进行更高级的定制
    public Label getTitleLabel() {
        return titleLabel;
    }
    
    // 获取计数Label
    public Label getItemCountLabel() {
        return itemCountLabel;
    }
    
    /**
     * 设置列表项的选择模式
     */
    public void setSelectionMode(SelectionMode mode) {
        listView.getSelectionModel().setSelectionMode(mode);
    }
    
    /**
     * 获取选中的项目
     */
    public T getSelectedItem() {
        return listView.getSelectionModel().getSelectedItem();
    }
    
    /**
     * 获取选中项目的索引
     */
    public int getSelectedIndex() {
        return listView.getSelectionModel().getSelectedIndex();
    }
    
    /**
     * 设置最大高度
     */
    public void setMaxListHeight(double height) {
        listView.setMaxHeight(height);
    }
    
    /**
     * 设置最小高度
     */
    public void setMinListHeight(double height) {
        listView.setMinHeight(height);
    }
    
    /**
     * 设置是否显示计数
     */
    public void setShowItemCount(boolean show) {
        itemCountLabel.setVisible(show);
        itemCountLabel.setManaged(show);
    }
}
