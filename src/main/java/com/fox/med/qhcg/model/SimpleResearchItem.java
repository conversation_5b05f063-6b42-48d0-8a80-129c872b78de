package com.fox.med.qhcg.model;

/**
 * 简化的医学研究项目数据模型
 * 不依赖JavaFX，使用普通Java属性
 */
public class SimpleResearchItem {
    
    private String title;
    private String researcher;
    private String status;
    private String date;
    private String department;
    private String priority;
    private String ethicsNumber;
    
    public SimpleResearchItem() {
        this("", "", "", "", "", "", "");
    }
    
    public SimpleResearchItem(String title, String researcher, String status, String date, String department) {
        this(title, researcher, status, date, department, "", "");
    }
    
    public SimpleResearchItem(String title, String researcher, String status, String date, String department, 
                             String priority, String ethicsNumber) {
        this.title = title;
        this.researcher = researcher;
        this.status = status;
        this.date = date;
        this.department = department;
        this.priority = priority;
        this.ethicsNumber = ethicsNumber;
    }
    
    // Title属性
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    // Researcher属性
    public String getResearcher() {
        return researcher;
    }
    
    public void setResearcher(String researcher) {
        this.researcher = researcher;
    }
    
    // Status属性
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    // Date属性
    public String getDate() {
        return date;
    }
    
    public void setDate(String date) {
        this.date = date;
    }
    
    // Department属性
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    // Priority属性
    public String getPriority() {
        return priority;
    }
    
    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    // EthicsNumber属性
    public String getEthicsNumber() {
        return ethicsNumber;
    }
    
    public void setEthicsNumber(String ethicsNumber) {
        this.ethicsNumber = ethicsNumber;
    }
    
    @Override
    public String toString() {
        return getTitle() + " - " + getResearcher();
    }
    
    /**
     * 获取格式化的显示文本
     */
    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        sb.append(getTitle());
        if (getResearcher() != null && !getResearcher().isEmpty()) {
            sb.append("\n研究者: ").append(getResearcher());
        }
        if (getDepartment() != null && !getDepartment().isEmpty()) {
            sb.append(" | 科室: ").append(getDepartment());
        }
        if (getStatus() != null && !getStatus().isEmpty()) {
            sb.append("\n状态: ").append(getStatus());
        }
        if (getDate() != null && !getDate().isEmpty()) {
            sb.append(" | 日期: ").append(getDate());
        }
        if (getPriority() != null && !getPriority().isEmpty()) {
            sb.append(" | 优先级: ").append(getPriority());
        }
        if (getEthicsNumber() != null && !getEthicsNumber().isEmpty()) {
            sb.append(" | 伦理编号: ").append(getEthicsNumber());
        }
        return sb.toString();
    }
    
    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        return title != null && !title.trim().isEmpty() &&
               researcher != null && !researcher.trim().isEmpty() &&
               status != null && !status.trim().isEmpty();
    }
    
    /**
     * 获取状态对应的CSS类名
     */
    public String getStatusCssClass() {
        if (status == null) return "status-pending";
        
        String statusLower = status.toLowerCase().trim();
        switch (statusLower) {
            case "待审核":
            case "pending":
            case "待提交":
                return "status-pending";
            case "已通过":
            case "approved":
            case "通过":
            case "批准":
                return "status-approved";
            case "已拒绝":
            case "rejected":
            case "拒绝":
            case "不通过":
                return "status-rejected";
            case "审核中":
            case "reviewing":
            case "评审中":
                return "status-reviewing";
            case "暂停":
            case "suspended":
            case "中止":
                return "status-suspended";
            default:
                return "status-pending";
        }
    }
}
