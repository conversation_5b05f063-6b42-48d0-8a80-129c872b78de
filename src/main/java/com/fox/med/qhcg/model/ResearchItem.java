package com.fox.med.qhcg.model;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * 医学研究伦理审查项目数据模型
 * 用于存储和管理医学研究项目的完整信息
 */
public class EthicsReviewProject {
    
    private final StringProperty title = new SimpleStringProperty();
    private final StringProperty researcher = new SimpleStringProperty();
    private final StringProperty status = new SimpleStringProperty();
    private final StringProperty date = new SimpleStringProperty();
    private final StringProperty department = new SimpleStringProperty();
    private final StringProperty priority = new SimpleStringProperty();
    private final StringProperty ethicsNumber = new SimpleStringProperty();
    
    public EthicsReviewProject() {
        this("", "", "", "", "", "", "");
    }

    public EthicsReviewProject(String title, String researcher, String status, String date, String department) {
        this(title, researcher, status, date, department, "", "");
    }

    public EthicsReviewProject(String title, String researcher, String status, String date, String department,
                              String priority, String ethicsNumber) {
        setTitle(title);
        setResearcher(researcher);
        setStatus(status);
        setDate(date);
        setDepartment(department);
        setPriority(priority);
        setEthicsNumber(ethicsNumber);
    }
    
    // Title属性
    public StringProperty titleProperty() {
        return title;
    }
    
    public String getTitle() {
        return title.get();
    }
    
    public void setTitle(String title) {
        this.title.set(title);
    }
    
    // Researcher属性
    public StringProperty researcherProperty() {
        return researcher;
    }
    
    public String getResearcher() {
        return researcher.get();
    }
    
    public void setResearcher(String researcher) {
        this.researcher.set(researcher);
    }
    
    // Status属性
    public StringProperty statusProperty() {
        return status;
    }
    
    public String getStatus() {
        return status.get();
    }
    
    public void setStatus(String status) {
        this.status.set(status);
    }
    
    // Date属性
    public StringProperty dateProperty() {
        return date;
    }
    
    public String getDate() {
        return date.get();
    }
    
    public void setDate(String date) {
        this.date.set(date);
    }
    
    // Department属性
    public StringProperty departmentProperty() {
        return department;
    }
    
    public String getDepartment() {
        return department.get();
    }
    
    public void setDepartment(String department) {
        this.department.set(department);
    }

    // Priority属性
    public StringProperty priorityProperty() {
        return priority;
    }

    public String getPriority() {
        return priority.get();
    }

    public void setPriority(String priority) {
        this.priority.set(priority);
    }

    // EthicsNumber属性
    public StringProperty ethicsNumberProperty() {
        return ethicsNumber;
    }

    public String getEthicsNumber() {
        return ethicsNumber.get();
    }

    public void setEthicsNumber(String ethicsNumber) {
        this.ethicsNumber.set(ethicsNumber);
    }

    @Override
    public String toString() {
        return getTitle() + " - " + getResearcher();
    }
    
    /**
     * 获取格式化的显示文本
     */
    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        sb.append(getTitle());
        if (!getResearcher().isEmpty()) {
            sb.append("\n研究者: ").append(getResearcher());
        }
        if (!getDepartment().isEmpty()) {
            sb.append(" | 科室: ").append(getDepartment());
        }
        if (!getStatus().isEmpty()) {
            sb.append("\n状态: ").append(getStatus());
        }
        if (!getDate().isEmpty()) {
            sb.append(" | 日期: ").append(getDate());
        }
        return sb.toString();
    }
}
