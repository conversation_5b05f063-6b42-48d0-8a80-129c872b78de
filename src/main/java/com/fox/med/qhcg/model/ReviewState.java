package com.fox.med.qhcg.model;

/**
 * 医学研究伦理审查状态枚举
 * 统一管理所有审核状态及其相关属性
 */
public enum ReviewState {
    
    /**
     * 待审核状态
     */
    PENDING("待审核", "status-pending", "#f59e0b", "#fef3c7", "#92400e"),
    
    /**
     * 已通过状态
     */
    APPROVED("已通过", "status-approved", "#10b981", "#d1fae5", "#065f46"),
    
    /**
     * 已拒绝状态
     */
    REJECTED("已拒绝", "status-rejected", "#ef4444", "#fee2e2", "#991b1b"),
    
    /**
     * 审核中状态
     */
    REVIEWING("审核中", "status-reviewing", "#6366f1", "#e0e7ff", "#3730a3"),
    
    /**
     * 暂停状态
     */
    SUSPENDED("暂停", "status-suspended", "#6b7280", "#f3f4f6", "#374151"),
    
    /**
     * 待提交状态
     */
    PENDING_SUBMISSION("待提交", "status-pending", "#f59e0b", "#fef3c7", "#92400e");
    
    private final String displayName;
    private final String cssClass;
    private final String borderColor;
    private final String backgroundColor;
    private final String textColor;
    
    ReviewState(String displayName, String cssClass, String borderColor, String backgroundColor, String textColor) {
        this.displayName = displayName;
        this.cssClass = cssClass;
        this.borderColor = borderColor;
        this.backgroundColor = backgroundColor;
        this.textColor = textColor;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取CSS样式类名
     */
    public String getCssClass() {
        return cssClass;
    }
    
    /**
     * 获取边框颜色
     */
    public String getBorderColor() {
        return borderColor;
    }
    
    /**
     * 获取背景颜色
     */
    public String getBackgroundColor() {
        return backgroundColor;
    }
    
    /**
     * 获取文本颜色
     */
    public String getTextColor() {
        return textColor;
    }
    
    /**
     * 根据字符串状态获取对应的ReviewState枚举
     * 支持中文和英文状态名称
     */
    public static ReviewState fromString(String status) {
        if (status == null || status.trim().isEmpty()) {
            return PENDING;
        }
        
        String statusLower = status.toLowerCase().trim();
        
        // 待审核状态的各种表示
        if (statusLower.equals("待审核") || statusLower.equals("pending")) {
            return PENDING;
        }
        
        // 已通过状态的各种表示
        if (statusLower.equals("已通过") || statusLower.equals("approved") || 
            statusLower.equals("通过") || statusLower.equals("批准")) {
            return APPROVED;
        }
        
        // 已拒绝状态的各种表示
        if (statusLower.equals("已拒绝") || statusLower.equals("rejected") || 
            statusLower.equals("拒绝") || statusLower.equals("不通过")) {
            return REJECTED;
        }
        
        // 审核中状态的各种表示
        if (statusLower.equals("审核中") || statusLower.equals("reviewing") || 
            statusLower.equals("评审中")) {
            return REVIEWING;
        }
        
        // 暂停状态的各种表示
        if (statusLower.equals("暂停") || statusLower.equals("suspended") || 
            statusLower.equals("中止")) {
            return SUSPENDED;
        }
        
        // 待提交状态的各种表示
        if (statusLower.equals("待提交") || statusLower.equals("pending_submission")) {
            return PENDING_SUBMISSION;
        }
        
        // 默认返回待审核状态
        return PENDING;
    }
    
    /**
     * 获取所有状态的显示名称数组
     */
    public static String[] getAllDisplayNames() {
        ReviewState[] states = values();
        String[] names = new String[states.length];
        for (int i = 0; i < states.length; i++) {
            names[i] = states[i].getDisplayName();
        }
        return names;
    }
    
    /**
     * 检查状态是否为最终状态（已通过或已拒绝）
     */
    public boolean isFinalState() {
        return this == APPROVED || this == REJECTED;
    }
    
    /**
     * 检查状态是否为进行中状态（待审核、审核中、待提交）
     */
    public boolean isInProgress() {
        return this == PENDING || this == REVIEWING || this == PENDING_SUBMISSION;
    }
    
    /**
     * 检查状态是否为暂停状态
     */
    public boolean isSuspended() {
        return this == SUSPENDED;
    }
    
    /**
     * 获取状态的优先级（用于排序）
     * 数值越小优先级越高
     */
    public int getPriority() {
        switch (this) {
            case REVIEWING -> {
                return 1;
            }
            case PENDING -> {
                return 2;
            }
            case PENDING_SUBMISSION -> {
                return 3;
            }
            case SUSPENDED -> {
                return 4;
            }
            case APPROVED -> {
                return 5;
            }
            case REJECTED -> {
                return 6;
            }
            default -> {
                return 999;
            }
        }
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
