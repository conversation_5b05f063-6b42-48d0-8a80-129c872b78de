package com.fox.med.qhcg.model;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * 通用表格数据项模型
 * 用于FlatTable组件的数据展示
 */
public class TableDataItem {
    
    private final StringProperty number = new SimpleStringProperty();
    private final StringProperty name = new SimpleStringProperty();
    private final StringProperty weight = new SimpleStringProperty();
    private final StringProperty symbol = new SimpleStringProperty();
    
    public TableDataItem() {
        this("", "", "", "");
    }
    
    public TableDataItem(String number, String name, String weight, String symbol) {
        setNumber(number);
        setName(name);
        setWeight(weight);
        setSymbol(symbol);
    }
    
    // Number属性
    public StringProperty numberProperty() {
        return number;
    }
    
    public String getNumber() {
        return number.get();
    }
    
    public void setNumber(String number) {
        this.number.set(number);
    }
    
    // Name属性
    public StringProperty nameProperty() {
        return name;
    }
    
    public String getName() {
        return name.get();
    }
    
    public void setName(String name) {
        this.name.set(name);
    }
    
    // Weight属性
    public StringProperty weightProperty() {
        return weight;
    }
    
    public String getWeight() {
        return weight.get();
    }
    
    public void setWeight(String weight) {
        this.weight.set(weight);
    }
    
    // Symbol属性
    public StringProperty symbolProperty() {
        return symbol;
    }
    
    public String getSymbol() {
        return symbol.get();
    }
    
    public void setSymbol(String symbol) {
        this.symbol.set(symbol);
    }
    
    @Override
    public String toString() {
        return getNumber() + " - " + getName() + " (" + getSymbol() + ")";
    }
}
