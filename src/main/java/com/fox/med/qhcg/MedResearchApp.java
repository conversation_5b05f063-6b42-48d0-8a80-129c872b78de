package com.fox.med.qhcg;

import com.fox.med.qhcg.model.ResearchItem;
import com.fox.med.qhcg.ui.cells.ResearchItemCell;
import com.fox.med.qhcg.ui.controls.HeaderList;
import com.fox.med.qhcg.ui.controls.ResearchHeaderList;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * 医学研究管理系统主应用程序
 * 展示HeaderList控件的使用
 */
public class MedResearchApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        // 创建主布局
        VBox root = new VBox(20);
        root.setPadding(new Insets(20));
        
        // 标题
        Label titleLabel = new Label("医学研究管理系统 - HeaderList控件演示");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;");
        
        // 创建水平布局容器
        HBox contentBox = new HBox(20);
        
        // 创建基础HeaderList示例
        HeaderList basicList = createBasicHeaderList();
        
        // 创建医学研究专用HeaderList示例
        ResearchHeaderList<ResearchItem> researchList = createResearchHeaderList();
        
        // 添加到水平容器
        contentBox.getChildren().addAll(basicList, researchList);
        
        // 创建滚动面板
        ScrollPane scrollPane = new ScrollPane(contentBox);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        
        // 添加到主布局
        root.getChildren().addAll(titleLabel, scrollPane);
        
        // 创建场景
        Scene scene = new Scene(root, 1000, 700);
        
        // 加载CSS样式
        scene.getStylesheets().add(getClass().getResource("/styles/header-list.css").toExternalForm());
        
        // 设置舞台
        primaryStage.setTitle("医学研究管理系统");
        primaryStage.setScene(scene);
        primaryStage.show();
    }
    
    /**
     * 创建基础HeaderList示例
     */
    private HeaderList createBasicHeaderList() {
        HeaderList list = new HeaderList("科室列表");
        
        // 添加示例数据
        list.addItems(
            "心内科",
            "神经内科", 
            "呼吸内科",
            "消化内科",
            "内分泌科",
            "肾内科",
            "血液科",
            "肿瘤科"
        );
        
        // 设置大小
        list.setPrefWidth(300);
        list.setMaxListHeight(400);
        
        return list;
    }
    
    /**
     * 创建医学研究专用HeaderList示例
     */
    private ResearchHeaderList<ResearchItem> createResearchHeaderList() {
        ResearchHeaderList<ResearchItem> list = new ResearchHeaderList<>("伦理审查项目");
        
        // 设置自定义单元格工厂
        list.setCellFactory(listView -> new ResearchItemCell());
        
        // 添加示例数据
        list.addItems(
            new ResearchItem(
                "冠心病患者PCI术后双联抗血小板治疗的临床研究",
                "张医生",
                "待审核",
                "2024-01-15",
                "心内科"
            ),
            new ResearchItem(
                "糖尿病肾病早期诊断标志物的研究",
                "李医生",
                "已通过",
                "2024-01-10",
                "内分泌科"
            ),
            new ResearchItem(
                "肺癌靶向治疗药物疗效评估",
                "王医生",
                "已拒绝",
                "2024-01-08",
                "肿瘤科"
            ),
            new ResearchItem(
                "脑卒中康复治疗新方法的临床验证",
                "赵医生",
                "待审核",
                "2024-01-20",
                "神经内科"
            ),
            new ResearchItem(
                "慢性阻塞性肺疾病患者生活质量改善研究",
                "陈医生",
                "已通过",
                "2024-01-12",
                "呼吸内科"
            )
        );
        
        // 设置大小
        list.setPrefWidth(500);
        list.setMaxListHeight(400);
        
        return list;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
