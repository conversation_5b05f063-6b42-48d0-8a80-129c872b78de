package com.fox.med.qhcg;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CSS样式文件验证工具
 * 验证HeaderList控件的样式定义
 */
public class ValidateStyles {
    
    public static void main(String[] args) {
        System.out.println("=== 医学研究管理系统 - CSS样式验证 ===\n");
        
        try {
            validateCSSFile();
            validateStyleClasses();
            validateColorScheme();
            
            System.out.println("✅ CSS样式验证完成！");
            System.out.println("\n📋 样式文件特性总结：");
            System.out.println("   • ✅ 医学专业主题色彩");
            System.out.println("   • ✅ 完整的状态样式系统");
            System.out.println("   • ✅ 响应式设计支持");
            System.out.println("   • ✅ 专业的滚动条样式");
            System.out.println("   • ✅ 优化的交互效果");
            
        } catch (Exception e) {
            System.err.println("❌ CSS样式验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void validateCSSFile() throws IOException {
        System.out.println("🔍 验证CSS样式文件...");
        
        InputStream cssStream = ValidateStyles.class.getResourceAsStream("/styles/header-list.css");
        if (cssStream == null) {
            throw new IOException("CSS文件未找到: /styles/header-list.css");
        }
        
        List<String> cssLines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(cssStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                cssLines.add(line);
            }
        }
        
        System.out.println("   ✓ CSS文件加载成功");
        System.out.println("   ✓ 总行数: " + cssLines.size());
        
        // 统计注释行
        long commentLines = cssLines.stream()
            .filter(line -> line.trim().startsWith("/*") || line.trim().startsWith("*"))
            .count();
        
        System.out.println("   ✓ 注释行数: " + commentLines);
        System.out.println("   ✓ 代码行数: " + (cssLines.size() - commentLines));
        System.out.println();
    }
    
    private static void validateStyleClasses() throws IOException {
        System.out.println("🎨 验证样式类定义...");
        
        String[] expectedClasses = {
            ".root",
            ".header-list",
            ".header-list-title", 
            ".header-list-view",
            ".research-header-list",
            ".research-header-title",
            ".research-header-count",
            ".research-list-view",
            ".research-item-title",
            ".research-item-researcher",
            ".research-item-status",
            ".research-item-date",
            ".status-pending",
            ".status-approved", 
            ".status-rejected",
            ".status-reviewing",
            ".status-suspended"
        };
        
        InputStream cssStream = ValidateStyles.class.getResourceAsStream("/styles/header-list.css");
        StringBuilder cssContent = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(cssStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                cssContent.append(line).append("\n");
            }
        }
        
        String css = cssContent.toString();
        
        for (String className : expectedClasses) {
            if (css.contains(className + " {") || css.contains(className + "{")) {
                System.out.println("   ✓ 样式类存在: " + className);
            } else {
                System.out.println("   ⚠ 样式类缺失: " + className);
            }
        }
        
        System.out.println("   ✓ 样式类验证完成");
        System.out.println();
    }
    
    private static void validateColorScheme() throws IOException {
        System.out.println("🌈 验证医学专业色彩方案...");
        
        InputStream cssStream = ValidateStyles.class.getResourceAsStream("/styles/header-list.css");
        StringBuilder cssContent = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(cssStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                cssContent.append(line).append("\n");
            }
        }
        
        String css = cssContent.toString();
        
        // 验证主要颜色
        String[][] colorScheme = {
            {"医学蓝色主题", "#1e3a8a", "主标题色"},
            {"专业蓝色", "#3b82f6", "交互色"},
            {"背景白色", "#ffffff", "主背景色"},
            {"浅灰背景", "#f8fafc", "次背景色"},
            {"边框灰色", "#d1d9e6", "边框色"},
            {"待审核橙色", "#fef3c7", "待审核状态"},
            {"通过绿色", "#d1fae5", "通过状态"},
            {"拒绝红色", "#fee2e2", "拒绝状态"},
            {"审核蓝色", "#e0e7ff", "审核中状态"},
            {"暂停灰色", "#f3f4f6", "暂停状态"}
        };
        
        for (String[] color : colorScheme) {
            if (css.contains(color[1])) {
                System.out.println("   ✓ " + color[0] + " (" + color[1] + ") - " + color[2]);
            } else {
                System.out.println("   ⚠ " + color[0] + " (" + color[1] + ") - 未找到");
            }
        }
        
        // 验证字体设置
        if (css.contains("Microsoft YaHei")) {
            System.out.println("   ✓ 中文字体: Microsoft YaHei");
        }
        
        if (css.contains("SimSun")) {
            System.out.println("   ✓ 备用中文字体: SimSun");
        }
        
        // 验证圆角设置
        Pattern borderRadiusPattern = Pattern.compile("-fx-border-radius:\\s*(\\d+)px");
        Matcher matcher = borderRadiusPattern.matcher(css);
        List<String> borderRadius = new ArrayList<>();
        while (matcher.find()) {
            borderRadius.add(matcher.group(1) + "px");
        }
        
        if (!borderRadius.isEmpty()) {
            System.out.println("   ✓ 圆角设置: " + String.join(", ", borderRadius));
        }
        
        System.out.println("   ✓ 色彩方案验证完成");
        System.out.println();
    }
}
