package com.fox.med.qhcg;

import com.fox.med.qhcg.model.EthicsReviewProject;
import com.fox.med.qhcg.model.TableDataItem;
import com.fox.med.qhcg.laf.components.HeaderList;
import com.fox.med.qhcg.laf.components.EthicsReviewList;
import com.fox.med.qhcg.laf.components.FlatTablePanel;
import com.fox.med.qhcg.laf.components.FlatTable;
import com.fox.med.qhcg.laf.renderers.EthicsReviewProjectRenderer;
import com.fox.med.qhcg.laf.panels.EthicsReviewDashboardPanel;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.util.Objects;

/**
 * 医学研究管理系统组件测试应用
 * 统一的Tab界面展示所有控件功能
 */
public class ComponentTestApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        // 创建Tab面板
        TabPane tabPane = new TabPane();
        tabPane.setTabClosingPolicy(TabPane.TabClosingPolicy.UNAVAILABLE);

        // 创建各个Tab
        Tab listTab = createListComponentsTab();
        Tab tableTab = createTableComponentsTab();
        Tab dashboardTab = createDashboardTab();

        // 添加Tab到面板
        tabPane.getTabs().addAll(listTab, tableTab, dashboardTab);

        // 创建场景
        Scene scene = new Scene(tabPane, 1200, 800);
        
        // 加载CSS样式
        scene.getStylesheets().add(Objects.requireNonNull(getClass().getResource("/styles/header-list.css")).toExternalForm());

        // 设置舞台
        primaryStage.setTitle("医学研究管理系统 - 组件测试");
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    /**
     * 创建列表组件Tab
     */
    private Tab createListComponentsTab() {
        Tab tab = new Tab("列表组件");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setStyle("-fx-background-color: #f8fafc;");

        // 标题
        Label titleLabel = new Label("列表组件演示");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1e3a8a;");

        // 基础列表面板
        HeaderList basicList = createBasicListPanel();
        
        // 伦理审查列表面板
        EthicsReviewListPanel<EthicsReviewProject> ethicsReviewList = createEthicsReviewListPanel();

        content.getChildren().addAll(titleLabel, basicList, ethicsReviewList);
        
        ScrollPane scrollPane = new ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: transparent;");
        
        tab.setContent(scrollPane);
        return tab;
    }

    /**
     * 创建表格组件Tab
     */
    private Tab createTableComponentsTab() {
        Tab tab = new Tab("表格组件");
        
        VBox content = new VBox(20);
        content.setPadding(new Insets(20));
        content.setStyle("-fx-background-color: #f8fafc;");

        // 标题
        Label titleLabel = new Label("表格组件演示");
        titleLabel.setStyle("-fx-font-size: 20px; -fx-font-weight: bold; -fx-text-fill: #1e3a8a;");

        // 带标题的表格面板
        FlatTablePanel titledTable = createTitledTablePanel();
        
        // 简洁的表格
        VBox simpleTableContainer = createSimpleTableContainer();

        content.getChildren().addAll(titleLabel, titledTable, simpleTableContainer);
        
        ScrollPane scrollPane = new ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: transparent;");
        
        tab.setContent(scrollPane);
        return tab;
    }

    /**
     * 创建仪表板Tab
     */
    private Tab createDashboardTab() {
        Tab tab = new Tab("综合仪表板");
        
        EthicsReviewDashboardPanel dashboard = new EthicsReviewDashboardPanel();
        
        ScrollPane scrollPane = new ScrollPane(dashboard);
        scrollPane.setFitToWidth(true);
        scrollPane.setStyle("-fx-background-color: transparent;");
        
        tab.setContent(scrollPane);
        return tab;
    }

    /**
     * 创建基础列表面板
     */
    private HeaderList createBasicListPanel() {
        HeaderList list = new HeaderList("医院科室");
        list.addItems(
            "心血管内科", "神经内科", "呼吸与危重症医学科", "消化内科",
            "内分泌代谢科", "肾脏内科", "血液科", "肿瘤内科"
        );
        list.setMaxListHeight(200);
        return list;
    }

    /**
     * 创建伦理审查列表面板
     */
    private EthicsReviewListPanel<EthicsReviewProject> createEthicsReviewListPanel() {
        EthicsReviewListPanel<EthicsReviewProject> list = new EthicsReviewListPanel<>("待审核项目");
        list.setCellFactory(listView -> new EthicsReviewProjectRenderer());
        
        list.addItems(
            new EthicsReviewProject(
                "新型冠状病毒疫苗安全性及免疫原性临床研究",
                "张志强 主任医师",
                "待审核",
                "2024-01-25",
                "感染科"
            ),

            new EthicsReviewProject(
                "儿童支气管哮喘雾化吸入治疗新方法临床验证",
                "王小红 副主任医师",
                "已通过",
                "2024-01-23",
                "儿科"
            )
        );
        
        list.setMaxListHeight(300);
        return list;
    }

    /**
     * 创建带标题的表格面板
     */
    private FlatTablePanel createTitledTablePanel() {
        FlatTablePanel table = new FlatTablePanel("化学元素周期表 (前5个元素)");
        
        table.addItems(
            new TableDataItem("1", "Hydrogen", "1.0079", "H"),
            new TableDataItem("2", "Helium", "4.0026", "He"),
            new TableDataItem("3", "Lithium", "6.941", "Li"),
            new TableDataItem("4", "Beryllium", "9.0122", "Be"),
            new TableDataItem("5", "Boron", "10.811", "B")
        );
        
        table.getTableView().setPrefHeight(200);
        return table;
    }

    /**
     * 创建简洁表格容器
     */
    private VBox createSimpleTableContainer() {
        VBox container = new VBox(10);
        
        // 添加说明标签
        Label label = new Label("简洁FlatTable (无标题、无圆角、无边框)");
        label.setStyle("-fx-font-size: 16px; -fx-font-weight: 600; -fx-text-fill: #1e3a8a;");
        
        // 创建简洁表格
        FlatTable table = new FlatTable();
        
        // 自定义列标题
        table.getNumberColumn().setText("编号");
        table.getNameColumn().setText("检查项目");
        table.getWeightColumn().setText("正常值");
        table.getSymbolColumn().setText("单位");
        
        table.addItems(
            new TableDataItem("001", "血红蛋白", "120-160", "g/L"),
            new TableDataItem("002", "白细胞计数", "4.0-10.0", "×10⁹/L"),
            new TableDataItem("003", "血小板计数", "100-300", "×10⁹/L"),
            new TableDataItem("004", "总胆固醇", "3.1-5.7", "mmol/L"),
            new TableDataItem("005", "血糖", "3.9-6.1", "mmol/L")
        );
        
        table.setTableHeight(200);
        
        container.getChildren().addAll(label, table);
        return container;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
