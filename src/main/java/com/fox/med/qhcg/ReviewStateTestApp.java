package com.fox.med.qhcg;

import com.fox.med.qhcg.model.ReviewState;
import com.fox.med.qhcg.model.EthicsReviewProject;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * ReviewState枚举测试应用
 * 展示ReviewState的各种功能和用法
 */
public class ReviewStateTestApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        VBox root = new VBox(15);
        root.setPadding(new Insets(20));
        root.setStyle("-fx-background-color: #f8fafc;");

        // 标题
        Label titleLabel = new Label("ReviewState 枚举测试");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1e3a8a;");

        // 测试所有状态
        Label allStatesLabel = new Label("所有审核状态:");
        allStatesLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: 600; -fx-text-fill: #374151;");

        VBox statesBox = new VBox(8);
        for (ReviewState state : ReviewState.values()) {
            Label stateLabel = new Label(String.format(
                "%s - CSS类: %s - 优先级: %d", 
                state.getDisplayName(), 
                state.getCssClass(), 
                state.getPriority()
            ));
            stateLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
            statesBox.getChildren().add(stateLabel);
        }

        // 测试字符串转换
        Label conversionLabel = new Label("字符串转换测试:");
        conversionLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: 600; -fx-text-fill: #374151;");

        VBox conversionBox = new VBox(8);
        String[] testStrings = {"待审核", "已通过", "rejected", "审核中", "suspended", "未知状态"};
        for (String testStr : testStrings) {
            ReviewState state = ReviewState.fromString(testStr);
            Label convLabel = new Label(String.format(
                "\"%s\" -> %s (%s)", 
                testStr, 
                state.getDisplayName(), 
                state.getCssClass()
            ));
            convLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");
            conversionBox.getChildren().add(convLabel);
        }

        // 测试项目状态
        Label projectLabel = new Label("项目状态测试:");
        projectLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: 600; -fx-text-fill: #374151;");

        VBox projectBox = new VBox(8);
        EthicsReviewProject project1 = new EthicsReviewProject(
            "测试项目1", "研究者A", "待审核", "2024-01-01", "心内科"
        );
        EthicsReviewProject project2 = new EthicsReviewProject(
            "测试项目2", "研究者B", "已通过", "2024-01-02", "神经科"
        );

        Label proj1Label = new Label(String.format(
            "项目1: %s - 状态: %s - 进行中: %s - 最终状态: %s", 
            project1.getTitle(),
            project1.getReviewState().getDisplayName(),
            project1.isInProgress(),
            project1.isFinalState()
        ));
        proj1Label.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");

        Label proj2Label = new Label(String.format(
            "项目2: %s - 状态: %s - 进行中: %s - 最终状态: %s", 
            project2.getTitle(),
            project2.getReviewState().getDisplayName(),
            project2.isInProgress(),
            project2.isFinalState()
        ));
        proj2Label.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");

        projectBox.getChildren().addAll(proj1Label, proj2Label);

        // 测试状态变更
        Label changeLabel = new Label("状态变更测试:");
        changeLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: 600; -fx-text-fill: #374151;");

        // 改变项目1的状态
        project1.setReviewState(ReviewState.APPROVED);
        Label changeResultLabel = new Label(String.format(
            "项目1状态变更后: %s - 字符串状态: %s - CSS类: %s", 
            project1.getReviewState().getDisplayName(),
            project1.getStatus(),
            project1.getStatusCssClass()
        ));
        changeResultLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");

        root.getChildren().addAll(
            titleLabel,
            allStatesLabel, statesBox,
            conversionLabel, conversionBox,
            projectLabel, projectBox,
            changeLabel, changeResultLabel
        );

        Scene scene = new Scene(root, 800, 600);
        primaryStage.setTitle("ReviewState 枚举测试");
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
