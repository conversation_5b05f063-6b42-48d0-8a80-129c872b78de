package com.fox.med.qhcg.ui;

import com.fox.med.qhcg.model.ResearchItem;
import com.fox.med.qhcg.ui.cells.ResearchItemCell;
import com.fox.med.qhcg.ui.controls.HeaderList;
import com.fox.med.qhcg.ui.controls.ResearchHeaderList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.Separator;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;

/**
 * 医学研究管理系统主仪表板
 * 展示多个HeaderList控件的综合使用
 */
public class MainDashboard extends VBox {
    
    private ResearchHeaderList<ResearchItem> pendingList;
    private ResearchHeaderList<ResearchItem> approvedList;
    private ResearchHeaderList<ResearchItem> rejectedList;
    private HeaderList departmentList;
    private HeaderList researcherList;
    
    public MainDashboard() {
        initializeComponents();
        setupLayout();
        loadSampleData();
    }
    
    private void initializeComponents() {
        // 创建各种列表控件
        pendingList = new ResearchHeaderList<>("待审核项目");
        pendingList.setCellFactory(listView -> new ResearchItemCell());
        
        approvedList = new ResearchHeaderList<>("已通过项目");
        approvedList.setCellFactory(listView -> new ResearchItemCell());
        
        rejectedList = new ResearchHeaderList<>("已拒绝项目");
        rejectedList.setCellFactory(listView -> new ResearchItemCell());
        
        departmentList = new HeaderList("参与科室");
        researcherList = new HeaderList("研究人员");
    }
    
    private void setupLayout() {
        this.setSpacing(20);
        this.setPadding(new Insets(20));
        
        // 标题区域
        Label titleLabel = new Label("医学研究伦理审查管理系统");
        titleLabel.setStyle("-fx-font-size: 28px; -fx-font-weight: bold; -fx-text-fill: #1a237e;");
        
        // 统计信息区域
        HBox statsBox = createStatsBox();
        
        // 分隔线
        Separator separator = new Separator();
        
        // 主内容区域
        GridPane contentGrid = new GridPane();
        contentGrid.setHgap(20);
        contentGrid.setVgap(20);
        
        // 设置列约束
        contentGrid.getColumnConstraints().addAll(
            new javafx.scene.layout.ColumnConstraints(300), // 左侧列
            new javafx.scene.layout.ColumnConstraints(400), // 中间列
            new javafx.scene.layout.ColumnConstraints(300)  // 右侧列
        );
        
        // 设置行约束
        contentGrid.getRowConstraints().addAll(
            new javafx.scene.layout.RowConstraints(350), // 第一行
            new javafx.scene.layout.RowConstraints(350)  // 第二行
        );
        
        // 添加控件到网格
        contentGrid.add(pendingList, 0, 0);
        contentGrid.add(approvedList, 1, 0);
        contentGrid.add(rejectedList, 2, 0);
        contentGrid.add(departmentList, 0, 1);
        contentGrid.add(researcherList, 1, 1);
        
        // 操作按钮区域
        HBox buttonBox = createButtonBox();
        
        // 添加到主布局
        this.getChildren().addAll(
            titleLabel,
            statsBox,
            separator,
            contentGrid,
            buttonBox
        );
        
        // 设置拉伸
        VBox.setVgrow(contentGrid, Priority.ALWAYS);
    }
    
    private HBox createStatsBox() {
        HBox statsBox = new HBox(30);
        statsBox.setAlignment(Pos.CENTER);
        statsBox.setPadding(new Insets(15));
        statsBox.setStyle("-fx-background-color: #f8f9fa; -fx-border-radius: 8px; -fx-background-radius: 8px;");
        
        // 创建统计卡片
        VBox totalCard = createStatCard("总项目数", "0", "#3498db");
        VBox pendingCard = createStatCard("待审核", "0", "#f39c12");
        VBox approvedCard = createStatCard("已通过", "0", "#27ae60");
        VBox rejectedCard = createStatCard("已拒绝", "0", "#e74c3c");
        
        statsBox.getChildren().addAll(totalCard, pendingCard, approvedCard, rejectedCard);
        
        return statsBox;
    }
    
    private VBox createStatCard(String title, String value, String color) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.setStyle("-fx-background-color: white; -fx-border-radius: 6px; -fx-background-radius: 6px; " +
                     "-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 3, 0, 0, 1);");
        
        Label titleLabel = new Label(title);
        titleLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #666666;");
        
        Label valueLabel = new Label(value);
        valueLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: " + color + ";");
        
        card.getChildren().addAll(valueLabel, titleLabel);
        
        return card;
    }
    
    private HBox createButtonBox() {
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.setPadding(new Insets(15, 0, 0, 0));
        
        Button addButton = new Button("新增项目");
        addButton.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 14px; " +
                          "-fx-padding: 10 20 10 20; -fx-border-radius: 5px; -fx-background-radius: 5px;");
        
        Button refreshButton = new Button("刷新数据");
        refreshButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 14px; " +
                              "-fx-padding: 10 20 10 20; -fx-border-radius: 5px; -fx-background-radius: 5px;");
        
        Button exportButton = new Button("导出报告");
        exportButton.setStyle("-fx-background-color: #9b59b6; -fx-text-fill: white; -fx-font-size: 14px; " +
                             "-fx-padding: 10 20 10 20; -fx-border-radius: 5px; -fx-background-radius: 5px;");
        
        buttonBox.getChildren().addAll(addButton, refreshButton, exportButton);
        
        return buttonBox;
    }
    
    private void loadSampleData() {
        // 加载待审核项目 - 使用更真实的医学研究数据
        pendingList.addItems(
            new ResearchItem(
                "新型冠状病毒疫苗安全性及免疫原性临床研究",
                "张志强 主任医师",
                "待审核",
                "2024-01-25",
                "感染科",
                "高",
                "BTCH-2024-001"
            ),
            new ResearchItem(
                "高血压患者ACEI/ARB类药物联合治疗方案优化研究",
                "李明华 教授",
                "待审核",
                "2024-01-24",
                "心内科",
                "中",
                "BTCH-2024-002"
            ),
            new ResearchItem(
                "儿童支气管哮喘雾化吸入治疗新方法临床验证",
                "王小红 副主任医师",
                "待提交",
                "2024-01-23",
                "儿科",
                "中",
                ""
            )
        );

        // 加载已通过项目
        approvedList.addItems(
            new ResearchItem(
                "2型糖尿病肾病早期诊断标志物及干预策略研究",
                "赵建国 主任医师",
                "已通过",
                "2024-01-20",
                "内分泌科",
                "高",
                "BTCH-2023-089"
            ),
            new ResearchItem(
                "阿尔茨海默病早期认知功能干预治疗效果评估",
                "陈丽娟 教授",
                "已通过",
                "2024-01-18",
                "神经内科",
                "高",
                "BTCH-2023-087"
            )
        );

        // 加载已拒绝项目
        rejectedList.addItems(
            new ResearchItem(
                "实验性抗肿瘤药物PD-1抑制剂临床试验",
                "刘永强 副教授",
                "已拒绝",
                "2024-01-15",
                "肿瘤科",
                "高",
                "BTCH-2024-003"
            )
        );
        
        // 加载科室列表
        departmentList.addItems(
            "心内科", "神经内科", "呼吸内科", "消化内科", 
            "内分泌科", "肾内科", "血液科", "肿瘤科", 
            "感染科", "儿科", "妇产科", "骨科"
        );
        
        // 加载研究人员列表
        researcherList.addItems(
            "张主任", "李教授", "王医生", "赵医生", 
            "陈教授", "刘医生", "周医生", "吴教授"
        );
    }
}
