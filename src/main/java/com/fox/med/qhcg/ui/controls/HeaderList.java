package com.fox.med.qhcg.ui.controls;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.layout.VBox;
import javafx.scene.layout.Priority;

/**
 * HeaderList控件 - 包含标题和列表的自定义控件
 * 适用于医学研究管理系统的数据展示
 */
public class HeaderList extends VBox {
    
    private final StringProperty headerText = new SimpleStringProperty();
    private final Label headerLabel;
    private final ListView<String> listView;
    private final ObservableList<String> items;
    
    public HeaderList() {
        this("");
    }
    
    public HeaderList(String headerText) {
        this.items = FXCollections.observableArrayList();
        
        // 创建标题标签
        this.headerLabel = new Label();
        this.headerLabel.getStyleClass().add("header-list-title");
        this.headerLabel.textProperty().bind(this.headerText);
        
        // 创建列表视图
        this.listView = new ListView<>(items);
        this.listView.getStyleClass().add("header-list-view");
        
        // 设置布局
        setupLayout();
        
        // 设置初始标题
        setHeaderText(headerText);
        
        // 应用样式类
        this.getStyleClass().add("header-list");
    }
    
    private void setupLayout() {
        // 设置间距和内边距 - 医学专业布局
        this.setSpacing(0);
        this.setPadding(new Insets(0));
        this.setAlignment(Pos.TOP_LEFT);

        // 标题不拉伸，列表拉伸填充剩余空间
        VBox.setVgrow(headerLabel, Priority.NEVER);
        VBox.setVgrow(listView, Priority.ALWAYS);

        // 添加子控件
        this.getChildren().addAll(headerLabel, listView);
    }
    
    // 标题文本属性
    public StringProperty headerTextProperty() {
        return headerText;
    }
    
    public String getHeaderText() {
        return headerText.get();
    }
    
    public void setHeaderText(String headerText) {
        this.headerText.set(headerText);
    }
    
    // 列表项操作
    public ObservableList<String> getItems() {
        return items;
    }
    
    public void addItem(String item) {
        items.add(item);
    }
    
    public void addItems(String... items) {
        this.items.addAll(items);
    }
    
    public void removeItem(String item) {
        items.remove(item);
    }
    
    public void clearItems() {
        items.clear();
    }
    
    // 获取ListView以便进行更高级的定制
    public ListView<String> getListView() {
        return listView;
    }
    
    // 获取标题Label以便进行更高级的定制
    public Label getHeaderLabel() {
        return headerLabel;
    }
    
    /**
     * 设置列表项的选择模式
     */
    public void setSelectionMode(javafx.scene.control.SelectionMode mode) {
        listView.getSelectionModel().setSelectionMode(mode);
    }
    
    /**
     * 获取选中的项目
     */
    public String getSelectedItem() {
        return listView.getSelectionModel().getSelectedItem();
    }
    
    /**
     * 获取选中项目的索引
     */
    public int getSelectedIndex() {
        return listView.getSelectionModel().getSelectedIndex();
    }
    
    /**
     * 设置最大高度
     */
    public void setMaxListHeight(double height) {
        listView.setMaxHeight(height);
    }
    
    /**
     * 设置最小高度
     */
    public void setMinListHeight(double height) {
        listView.setMinHeight(height);
    }
}
