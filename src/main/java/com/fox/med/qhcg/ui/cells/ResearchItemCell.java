package com.fox.med.qhcg.ui.cells;

import com.fox.med.qhcg.model.ResearchItem;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;

/**
 * 医学研究项目的自定义列表单元格
 */
public class ResearchItemCell extends ListCell<ResearchItem> {
    
    private final VBox container;
    private final Label titleLabel;
    private final Label researcherLabel;
    private final Label statusLabel;
    private final Label dateLabel;
    private final HBox bottomBox;
    
    public ResearchItemCell() {
        // 创建标题标签
        titleLabel = new Label();
        titleLabel.getStyleClass().add("research-item-title");
        titleLabel.setWrapText(true);
        
        // 创建研究者标签
        researcherLabel = new Label();
        researcherLabel.getStyleClass().add("research-item-researcher");
        
        // 创建状态标签
        statusLabel = new Label();
        statusLabel.getStyleClass().add("research-item-status");
        
        // 创建日期标签
        dateLabel = new Label();
        dateLabel.getStyleClass().add("research-item-date");
        
        // 底部信息容器
        bottomBox = new HBox(10);
        bottomBox.setAlignment(Pos.CENTER_LEFT);
        bottomBox.getChildren().addAll(researcherLabel, statusLabel, dateLabel);
        
        // 主容器
        container = new VBox(5);
        container.setPadding(new Insets(8));
        container.getChildren().addAll(titleLabel, bottomBox);
        
        // 设置容器拉伸
        VBox.setVgrow(titleLabel, Priority.NEVER);
        HBox.setHgrow(researcherLabel, Priority.ALWAYS);
    }
    
    @Override
    protected void updateItem(ResearchItem item, boolean empty) {
        super.updateItem(item, empty);
        
        if (empty || item == null) {
            setGraphic(null);
            setText(null);
        } else {
            // 设置标题
            titleLabel.setText(item.getTitle());
            
            // 设置研究者信息
            if (item.getResearcher() != null && !item.getResearcher().isEmpty()) {
                researcherLabel.setText("研究者: " + item.getResearcher());
                researcherLabel.setVisible(true);
                researcherLabel.setManaged(true);
            } else {
                researcherLabel.setVisible(false);
                researcherLabel.setManaged(false);
            }
            
            // 设置状态信息
            if (item.getStatus() != null && !item.getStatus().isEmpty()) {
                statusLabel.setText("状态: " + item.getStatus());
                statusLabel.setVisible(true);
                statusLabel.setManaged(true);
                
                // 根据状态设置不同的样式
                statusLabel.getStyleClass().removeAll("status-pending", "status-approved", "status-rejected");
                switch (item.getStatus().toLowerCase()) {
                    case "待审核":
                    case "pending":
                        statusLabel.getStyleClass().add("status-pending");
                        break;
                    case "已通过":
                    case "approved":
                        statusLabel.getStyleClass().add("status-approved");
                        break;
                    case "已拒绝":
                    case "rejected":
                        statusLabel.getStyleClass().add("status-rejected");
                        break;
                }
            } else {
                statusLabel.setVisible(false);
                statusLabel.setManaged(false);
            }
            
            // 设置日期信息
            if (item.getDate() != null && !item.getDate().isEmpty()) {
                dateLabel.setText("日期: " + item.getDate());
                dateLabel.setVisible(true);
                dateLabel.setManaged(true);
            } else {
                dateLabel.setVisible(false);
                dateLabel.setManaged(false);
            }
            
            setGraphic(container);
            setText(null);
        }
    }
}
