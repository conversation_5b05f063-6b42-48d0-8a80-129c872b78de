package com.fox.med.qhcg.ui.cells;

import com.fox.med.qhcg.model.ResearchItem;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;

/**
 * 医学研究项目的自定义列表单元格
 */
public class ResearchItemCell extends ListCell<ResearchItem> {
    
    private final VBox container;
    private final Label titleLabel;
    private final Label researcherLabel;
    private final Label statusLabel;
    private final Label dateLabel;
    private final HBox bottomBox;
    
    public ResearchItemCell() {
        // 创建标题标签 - 医学专业样式
        titleLabel = new Label();
        titleLabel.getStyleClass().add("research-item-title");
        titleLabel.setWrapText(true);
        titleLabel.setMaxWidth(Double.MAX_VALUE);

        // 创建研究者标签
        researcherLabel = new Label();
        researcherLabel.getStyleClass().add("research-item-researcher");

        // 创建状态标签
        statusLabel = new Label();
        statusLabel.getStyleClass().add("research-item-status");

        // 创建日期标签
        dateLabel = new Label();
        dateLabel.getStyleClass().add("research-item-date");

        // 底部信息容器 - 改进布局
        bottomBox = new HBox(12);
        bottomBox.setAlignment(Pos.CENTER_LEFT);
        bottomBox.getChildren().addAll(researcherLabel, statusLabel, dateLabel);

        // 主容器 - 医学专业间距
        container = new VBox(8);
        container.setPadding(new Insets(16, 20, 16, 20));
        container.getChildren().addAll(titleLabel, bottomBox);

        // 设置容器拉伸
        VBox.setVgrow(titleLabel, Priority.NEVER);
        HBox.setHgrow(researcherLabel, Priority.ALWAYS);

        // 设置容器样式
        container.setMaxWidth(Double.MAX_VALUE);
    }
    
    @Override
    protected void updateItem(ResearchItem item, boolean empty) {
        super.updateItem(item, empty);
        
        if (empty || item == null) {
            setGraphic(null);
            setText(null);
        } else {
            // 设置标题
            titleLabel.setText(item.getTitle());
            
            // 设置研究者信息
            if (item.getResearcher() != null && !item.getResearcher().isEmpty()) {
                researcherLabel.setText("研究者: " + item.getResearcher());
                researcherLabel.setVisible(true);
                researcherLabel.setManaged(true);
            } else {
                researcherLabel.setVisible(false);
                researcherLabel.setManaged(false);
            }
            
            // 设置状态信息 - 医学专业状态处理
            if (item.getStatus() != null && !item.getStatus().isEmpty()) {
                statusLabel.setText(item.getStatus());
                statusLabel.setVisible(true);
                statusLabel.setManaged(true);

                // 清除所有状态样式
                statusLabel.getStyleClass().removeAll(
                    "status-pending", "status-approved", "status-rejected",
                    "status-reviewing", "status-suspended"
                );

                // 根据状态设置不同的样式 - 支持更多医学研究状态
                String status = item.getStatus().toLowerCase().trim();
                switch (status) {
                    case "待审核":
                    case "pending":
                    case "待提交":
                        statusLabel.getStyleClass().add("status-pending");
                        break;
                    case "已通过":
                    case "approved":
                    case "通过":
                    case "批准":
                        statusLabel.getStyleClass().add("status-approved");
                        break;
                    case "已拒绝":
                    case "rejected":
                    case "拒绝":
                    case "不通过":
                        statusLabel.getStyleClass().add("status-rejected");
                        break;
                    case "审核中":
                    case "reviewing":
                    case "评审中":
                        statusLabel.getStyleClass().add("status-reviewing");
                        break;
                    case "暂停":
                    case "suspended":
                    case "中止":
                        statusLabel.getStyleClass().add("status-suspended");
                        break;
                    default:
                        statusLabel.getStyleClass().add("status-pending");
                        break;
                }
            } else {
                statusLabel.setVisible(false);
                statusLabel.setManaged(false);
            }
            
            // 设置日期信息
            if (item.getDate() != null && !item.getDate().isEmpty()) {
                dateLabel.setText("日期: " + item.getDate());
                dateLabel.setVisible(true);
                dateLabel.setManaged(true);
            } else {
                dateLabel.setVisible(false);
                dateLabel.setManaged(false);
            }
            
            setGraphic(container);
            setText(null);
        }
    }
}
