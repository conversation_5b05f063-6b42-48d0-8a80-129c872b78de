package com.fox.med.qhcg.ui.controls;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.util.Callback;

/**
 * 医学研究专用的HeaderList控件
 * 支持自定义数据模型和更丰富的显示格式
 */
public class ResearchHeaderList<T> extends VBox {
    
    private final StringProperty headerText = new SimpleStringProperty();
    private final Label headerLabel;
    private final Label countLabel;
    private final ListView<T> listView;
    private final ObservableList<T> items;
    private final HBox headerBox;
    
    public ResearchHeaderList() {
        this("");
    }
    
    public ResearchHeaderList(String headerText) {
        this.items = FXCollections.observableArrayList();
        
        // 创建标题区域
        this.headerLabel = new Label();
        this.headerLabel.getStyleClass().add("research-header-title");
        this.headerLabel.textProperty().bind(this.headerText);
        
        // 创建计数标签
        this.countLabel = new Label("(0)");
        this.countLabel.getStyleClass().add("research-header-count");
        
        // 标题容器
        this.headerBox = new HBox(10);
        this.headerBox.setAlignment(Pos.CENTER_LEFT);
        this.headerBox.getChildren().addAll(headerLabel, countLabel);
        
        // 创建列表视图
        this.listView = new ListView<>(items);
        this.listView.getStyleClass().add("research-list-view");
        
        // 监听列表项数量变化
        items.addListener((javafx.collections.ListChangeListener<T>) change -> {
            updateCountLabel();
        });
        
        // 设置布局
        setupLayout();
        
        // 设置初始标题
        setHeaderText(headerText);
        
        // 应用样式类
        this.getStyleClass().add("research-header-list");
    }
    
    private void setupLayout() {
        // 设置间距和内边距 - 医学专业布局
        this.setSpacing(0);
        this.setPadding(new Insets(0));
        this.setAlignment(Pos.TOP_LEFT);

        // 设置标题区域样式
        this.headerBox.getStyleClass().add("research-header-box");
        this.headerBox.setPadding(new Insets(20, 20, 16, 20));

        // 标题不拉伸，列表拉伸填充剩余空间
        VBox.setVgrow(headerBox, Priority.NEVER);
        VBox.setVgrow(listView, Priority.ALWAYS);

        // 添加子控件
        this.getChildren().addAll(headerBox, listView);
    }
    
    private void updateCountLabel() {
        countLabel.setText("(" + items.size() + ")");
    }
    
    // 标题文本属性
    public StringProperty headerTextProperty() {
        return headerText;
    }
    
    public String getHeaderText() {
        return headerText.get();
    }
    
    public void setHeaderText(String headerText) {
        this.headerText.set(headerText);
    }
    
    // 列表项操作
    public ObservableList<T> getItems() {
        return items;
    }
    
    public void addItem(T item) {
        items.add(item);
    }
    
    @SafeVarargs
    public final void addItems(T... items) {
        this.items.addAll(items);
    }
    
    public void removeItem(T item) {
        items.remove(item);
    }
    
    public void clearItems() {
        items.clear();
    }
    
    // 设置自定义单元格工厂
    public void setCellFactory(Callback<ListView<T>, ListCell<T>> cellFactory) {
        listView.setCellFactory(cellFactory);
    }
    
    // 获取ListView以便进行更高级的定制
    public ListView<T> getListView() {
        return listView;
    }
    
    // 获取标题Label以便进行更高级的定制
    public Label getHeaderLabel() {
        return headerLabel;
    }
    
    // 获取计数Label
    public Label getCountLabel() {
        return countLabel;
    }
    
    /**
     * 设置列表项的选择模式
     */
    public void setSelectionMode(SelectionMode mode) {
        listView.getSelectionModel().setSelectionMode(mode);
    }
    
    /**
     * 获取选中的项目
     */
    public T getSelectedItem() {
        return listView.getSelectionModel().getSelectedItem();
    }
    
    /**
     * 获取选中项目的索引
     */
    public int getSelectedIndex() {
        return listView.getSelectionModel().getSelectedIndex();
    }
    
    /**
     * 设置最大高度
     */
    public void setMaxListHeight(double height) {
        listView.setMaxHeight(height);
    }
    
    /**
     * 设置最小高度
     */
    public void setMinListHeight(double height) {
        listView.setMinHeight(height);
    }
    
    /**
     * 设置是否显示计数
     */
    public void setShowCount(boolean show) {
        countLabel.setVisible(show);
        countLabel.setManaged(show);
    }
}
