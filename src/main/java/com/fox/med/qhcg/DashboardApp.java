package com.fox.med.qhcg;

import com.fox.med.qhcg.laf.panels.EthicsReviewDashboardPanel;
import javafx.application.Application;
import javafx.scene.Scene;
import javafx.stage.Stage;

/**
 * 仪表板应用程序启动类
 */
public class DashboardApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        // 创建主仪表板
        EthicsReviewDashboardPanel dashboard = new EthicsReviewDashboardPanel();
        
        // 创建场景
        Scene scene = new Scene(dashboard, 1200, 800);
        
        // 加载CSS样式
        scene.getStylesheets().add(getClass().getResource("/styles/header-list.css").toExternalForm());
        
        // 设置舞台
        primaryStage.setTitle("医学研究伦理审查管理系统 - 仪表板");
        primaryStage.setScene(scene);
        primaryStage.setMaximized(true);
        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
