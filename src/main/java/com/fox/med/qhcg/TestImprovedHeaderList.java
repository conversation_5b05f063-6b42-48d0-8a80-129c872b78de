package com.fox.med.qhcg;

import com.fox.med.qhcg.model.ResearchItem;
import com.fox.med.qhcg.ui.cells.ResearchItemCell;
import com.fox.med.qhcg.ui.controls.HeaderList;
import com.fox.med.qhcg.ui.controls.ResearchHeaderList;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * 测试改进后的HeaderList控件
 * 展示医学专业主题的UI设计
 */
public class TestImprovedHeaderList extends Application {

    @Override
    public void start(Stage primaryStage) {
        // 创建主布局
        VBox root = new VBox(24);
        root.setPadding(new Insets(24));
        root.setStyle("-fx-background-color: #f8fafc;");
        
        // 标题
        Label titleLabel = new Label("医学研究管理系统 - 改进版HeaderList控件");
        titleLabel.setStyle(
            "-fx-font-size: 28px; " +
            "-fx-font-weight: 700; " +
            "-fx-text-fill: #0f172a; " +
            "-fx-padding: 0 0 16 0;"
        );
        
        // 副标题
        Label subtitleLabel = new Label("基于医学专业UI设计规范的HeaderList控件展示");
        subtitleLabel.setStyle(
            "-fx-font-size: 16px; " +
            "-fx-text-fill: #64748b; " +
            "-fx-padding: 0 0 24 0;"
        );
        
        // 创建网格布局
        GridPane gridPane = new GridPane();
        gridPane.setHgap(24);
        gridPane.setVgap(24);
        
        // 创建各种HeaderList示例
        HeaderList departmentList = createDepartmentList();
        HeaderList statusList = createStatusList();
        ResearchHeaderList<ResearchItem> pendingList = createPendingResearchList();
        ResearchHeaderList<ResearchItem> approvedList = createApprovedResearchList();
        
        // 添加到网格
        gridPane.add(departmentList, 0, 0);
        gridPane.add(statusList, 1, 0);
        gridPane.add(pendingList, 0, 1);
        gridPane.add(approvedList, 1, 1);
        
        // 设置列约束
        gridPane.getColumnConstraints().addAll(
            new javafx.scene.layout.ColumnConstraints(400),
            new javafx.scene.layout.ColumnConstraints(400)
        );
        
        // 创建滚动面板
        ScrollPane scrollPane = new ScrollPane(gridPane);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        scrollPane.setStyle("-fx-background-color: transparent;");
        
        // 添加到主布局
        root.getChildren().addAll(titleLabel, subtitleLabel, scrollPane);
        
        // 创建场景
        Scene scene = new Scene(root, 1000, 800);
        
        // 加载CSS样式
        scene.getStylesheets().add(getClass().getResource("/styles/header-list.css").toExternalForm());
        
        // 设置舞台
        primaryStage.setTitle("医学研究管理系统 - 改进版HeaderList控件测试");
        primaryStage.setScene(scene);
        primaryStage.show();
    }
    
    private HeaderList createDepartmentList() {
        HeaderList list = new HeaderList("医院科室");
        
        list.addItems(
            "心血管内科",
            "神经内科", 
            "呼吸与危重症医学科",
            "消化内科",
            "内分泌代谢科",
            "肾脏内科",
            "血液科",
            "肿瘤内科",
            "感染科",
            "儿科",
            "妇产科",
            "骨科",
            "神经外科",
            "胸外科"
        );
        
        list.setPrefWidth(380);
        list.setMaxListHeight(300);
        
        return list;
    }
    
    private HeaderList createStatusList() {
        HeaderList list = new HeaderList("审核状态");
        
        list.addItems(
            "待提交",
            "待审核",
            "审核中",
            "已通过",
            "已拒绝",
            "暂停",
            "中止"
        );
        
        list.setPrefWidth(380);
        list.setMaxListHeight(300);
        
        return list;
    }
    
    private ResearchHeaderList<ResearchItem> createPendingResearchList() {
        ResearchHeaderList<ResearchItem> list = new ResearchHeaderList<>("待审核项目");
        list.setCellFactory(listView -> new ResearchItemCell());
        
        list.addItems(
            new ResearchItem(
                "新型冠状病毒疫苗安全性及免疫原性临床研究",
                "张志强 主任医师",
                "待审核",
                "2024-01-25",
                "感染科",
                "高",
                "BTCH-2024-001"
            ),
            new ResearchItem(
                "高血压患者ACEI/ARB类药物联合治疗方案优化研究",
                "李明华 教授",
                "待审核",
                "2024-01-24",
                "心内科",
                "中",
                "BTCH-2024-002"
            ),
            new ResearchItem(
                "儿童支气管哮喘雾化吸入治疗新方法临床验证",
                "王小红 副主任医师",
                "待提交",
                "2024-01-23",
                "儿科",
                "中",
                ""
            )
        );
        
        list.setPrefWidth(380);
        list.setMaxListHeight(350);
        
        return list;
    }
    
    private ResearchHeaderList<ResearchItem> createApprovedResearchList() {
        ResearchHeaderList<ResearchItem> list = new ResearchHeaderList<>("已通过项目");
        list.setCellFactory(listView -> new ResearchItemCell());
        
        list.addItems(
            new ResearchItem(
                "2型糖尿病肾病早期诊断标志物及干预策略研究",
                "赵建国 主任医师",
                "已通过",
                "2024-01-20",
                "内分泌科",
                "高",
                "BTCH-2023-089"
            ),
            new ResearchItem(
                "阿尔茨海默病早期认知功能干预治疗效果评估",
                "陈丽娟 教授",
                "已通过",
                "2024-01-18",
                "神经内科",
                "高",
                "BTCH-2023-087"
            ),
            new ResearchItem(
                "慢性阻塞性肺疾病急性加重期治疗方案比较研究",
                "周建华 副主任医师",
                "已通过",
                "2024-01-16",
                "呼吸科",
                "中",
                "BTCH-2023-085"
            )
        );
        
        list.setPrefWidth(380);
        list.setMaxListHeight(350);
        
        return list;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
