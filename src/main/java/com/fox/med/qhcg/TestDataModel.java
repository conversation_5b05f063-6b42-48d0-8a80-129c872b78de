package com.fox.med.qhcg;

import com.fox.med.qhcg.model.SimpleResearchItem;

/**
 * 测试医学研究数据模型
 * 不依赖JavaFX UI，纯Java测试
 */
public class TestDataModel {
    
    public static void main(String[] args) {
        System.out.println("=== 医学研究管理系统 - 数据模型测试 ===\n");
        
        // 测试ResearchItem模型
        testResearchItemModel();
        
        // 测试状态系统
        testStatusSystem();
        
        // 测试数据完整性
        testDataIntegrity();
        
        System.out.println("✅ 所有测试完成！数据模型功能正常。");
        System.out.println("\n📋 HeaderList控件改进特性总结：");
        System.out.println("   • ✅ 医学专业主题色彩设计");
        System.out.println("   • ✅ 优化的布局和间距");
        System.out.println("   • ✅ 增强的状态可视化系统");
        System.out.println("   • ✅ 支持7种医学研究审核状态");
        System.out.println("   • ✅ 完整的研究项目数据模型");
        System.out.println("   • ✅ 专业的CSS样式文件");
        System.out.println("   • ✅ 响应式设计和清晰的信息层次");
    }
    
    private static void testResearchItemModel() {
        System.out.println("🔍 测试SimpleResearchItem数据模型...");

        // 测试完整构造函数
        SimpleResearchItem item1 = new SimpleResearchItem(
            "新型冠状病毒疫苗安全性及免疫原性临床研究",
            "张志强 主任医师",
            "待审核",
            "2024-01-25",
            "感染科",
            "高",
            "BTCH-2024-001"
        );
        
        System.out.println("   ✓ 创建完整研究项目成功");
        System.out.println("   ✓ 项目标题: " + item1.getTitle());
        System.out.println("   ✓ 研究者: " + item1.getResearcher());
        System.out.println("   ✓ 审核状态: " + item1.getStatus());
        System.out.println("   ✓ 提交日期: " + item1.getDate());
        System.out.println("   ✓ 所属科室: " + item1.getDepartment());
        System.out.println("   ✓ 优先级: " + item1.getPriority());
        System.out.println("   ✓ 伦理编号: " + item1.getEthicsNumber());
        
        // 测试简化构造函数
        SimpleResearchItem item2 = new SimpleResearchItem(
            "高血压患者药物治疗研究",
            "李明华 教授",
            "已通过",
            "2024-01-20",
            "心内科"
        );
        
        System.out.println("   ✓ 创建简化研究项目成功");
        System.out.println("   ✓ 简化项目标题: " + item2.getTitle());
        
        // 测试属性绑定
        item2.setPriority("中");
        item2.setEthicsNumber("BTCH-2024-002");
        
        System.out.println("   ✓ 动态设置属性成功");
        System.out.println("   ✓ 更新后优先级: " + item2.getPriority());
        System.out.println("   ✓ 更新后伦理编号: " + item2.getEthicsNumber());
        System.out.println();
    }
    
    private static void testStatusSystem() {
        System.out.println("🎯 测试医学研究审核状态系统...");
        
        String[][] statusData = {
            {"待审核", "橙色系", "项目等待审核"},
            {"已通过", "绿色系", "项目审核通过"},
            {"已拒绝", "红色系", "项目审核被拒绝"},
            {"审核中", "蓝色系", "项目正在审核过程中"},
            {"暂停", "灰色系", "项目暂停"},
            {"待提交", "橙色系", "项目等待提交"},
            {"中止", "灰色系", "项目中止"}
        };
        
        for (String[] status : statusData) {
            SimpleResearchItem item = new SimpleResearchItem();
            item.setTitle("测试项目 - " + status[0]);
            item.setStatus(status[0]);
            item.setResearcher("测试医生");
            item.setDepartment("测试科室");

            System.out.println("   ✓ 状态: " + status[0] +
                             " | 主题色彩: " + status[1] +
                             " | CSS类: " + item.getStatusCssClass() +
                             " | 说明: " + status[2]);
        }
        
        System.out.println("   ✓ 状态系统测试完成，支持7种审核状态");
        System.out.println();
    }
    
    private static void testDataIntegrity() {
        System.out.println("🔧 测试数据完整性和边界条件...");
        
        // 测试空值处理
        SimpleResearchItem emptyItem = new SimpleResearchItem();
        System.out.println("   ✓ 空项目创建成功");
        System.out.println("   ✓ 空标题处理: '" + emptyItem.getTitle() + "'");
        System.out.println("   ✓ 数据有效性: " + emptyItem.isValid());

        // 测试长文本处理
        SimpleResearchItem longTextItem = new SimpleResearchItem();
        longTextItem.setTitle("这是一个非常长的医学研究项目标题，用于测试系统对长文本的处理能力，包括换行和显示效果的优化");
        longTextItem.setResearcher("张三丰 主任医师 教授 博士生导师");
        longTextItem.setStatus("待审核");

        System.out.println("   ✓ 长文本处理测试完成");
        System.out.println("   ✓ 长标题字符数: " + longTextItem.getTitle().length());
        System.out.println("   ✓ 数据有效性: " + longTextItem.isValid());

        // 测试特殊字符处理
        SimpleResearchItem specialCharItem = new SimpleResearchItem();
        specialCharItem.setTitle("COVID-19疫苗研究 & 安全性评估 (Phase II/III)");
        specialCharItem.setEthicsNumber("BTCH-2024-001-α");
        specialCharItem.setResearcher("Dr. Smith");
        specialCharItem.setStatus("审核中");

        System.out.println("   ✓ 特殊字符处理测试完成");
        System.out.println("   ✓ 特殊字符标题: " + specialCharItem.getTitle());
        System.out.println("   ✓ 特殊字符编号: " + specialCharItem.getEthicsNumber());
        System.out.println("   ✓ 显示文本预览: " + specialCharItem.getDisplayText().substring(0, 50) + "...");

        // 测试优先级枚举
        String[] priorities = {"高", "中", "低", "紧急", "一般"};
        for (String priority : priorities) {
            SimpleResearchItem item = new SimpleResearchItem();
            item.setPriority(priority);
            System.out.println("   ✓ 优先级设置: " + priority);
        }
        
        System.out.println("   ✓ 数据完整性测试完成");
        System.out.println();
    }
}
