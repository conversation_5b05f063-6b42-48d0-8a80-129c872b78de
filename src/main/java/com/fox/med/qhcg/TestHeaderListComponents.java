package com.fox.med.qhcg;

import com.fox.med.qhcg.model.ResearchItem;
import com.fox.med.qhcg.ui.controls.HeaderList;
import com.fox.med.qhcg.ui.controls.ResearchHeaderList;

/**
 * 简单的HeaderList组件测试类
 * 用于验证组件的基本功能
 */
public class TestHeaderListComponents {
    
    public static void main(String[] args) {
        System.out.println("=== 医学研究管理系统 - HeaderList控件测试 ===\n");
        
        // 测试基础HeaderList
        testBasicHeaderList();
        
        // 测试ResearchHeaderList
        testResearchHeaderList();
        
        // 测试ResearchItem模型
        testResearchItemModel();
        
        System.out.println("✅ 所有测试完成！HeaderList控件功能正常。");
        System.out.println("\n📋 改进特性总结：");
        System.out.println("   • 医学专业主题色彩设计");
        System.out.println("   • 优化的布局和间距");
        System.out.println("   • 增强的状态可视化系统");
        System.out.println("   • 支持7种医学研究审核状态");
        System.out.println("   • 专业的滚动条和交互效果");
        System.out.println("   • 响应式设计和清晰的信息层次");
    }
    
    private static void testBasicHeaderList() {
        System.out.println("🔍 测试基础HeaderList控件...");
        
        HeaderList departmentList = new HeaderList("医院科室");
        
        // 添加科室数据
        departmentList.addItems(
            "心血管内科",
            "神经内科", 
            "呼吸与危重症医学科",
            "消化内科",
            "内分泌代谢科",
            "肾脏内科",
            "血液科",
            "肿瘤内科"
        );
        
        System.out.println("   ✓ 创建科室列表成功");
        System.out.println("   ✓ 标题: " + departmentList.getHeaderText());
        System.out.println("   ✓ 科室数量: " + departmentList.getItems().size());
        System.out.println("   ✓ 样式类: " + departmentList.getStyleClass());
        System.out.println();
    }
    
    private static void testResearchHeaderList() {
        System.out.println("🔍 测试医学研究专用HeaderList控件...");
        
        ResearchHeaderList<ResearchItem> researchList = new ResearchHeaderList<>("伦理审查项目");
        
        // 添加研究项目数据
        researchList.addItems(
            new ResearchItem(
                "新型冠状病毒疫苗安全性及免疫原性临床研究",
                "张志强 主任医师",
                "待审核",
                "2024-01-25",
                "感染科",
                "高",
                "BTCH-2024-001"
            ),
            new ResearchItem(
                "高血压患者ACEI/ARB类药物联合治疗方案优化研究",
                "李明华 教授",
                "已通过",
                "2024-01-24",
                "心内科",
                "中",
                "BTCH-2024-002"
            ),
            new ResearchItem(
                "2型糖尿病肾病早期诊断标志物及干预策略研究",
                "赵建国 主任医师",
                "已拒绝",
                "2024-01-20",
                "内分泌科",
                "高",
                "BTCH-2023-089"
            )
        );
        
        System.out.println("   ✓ 创建研究项目列表成功");
        System.out.println("   ✓ 标题: " + researchList.getHeaderText());
        System.out.println("   ✓ 项目数量: " + researchList.getItems().size());
        System.out.println("   ✓ 计数标签: " + researchList.getCountLabel().getText());
        System.out.println("   ✓ 样式类: " + researchList.getStyleClass());
        System.out.println();
    }
    
    private static void testResearchItemModel() {
        System.out.println("🔍 测试ResearchItem数据模型...");
        
        ResearchItem item = new ResearchItem(
            "阿尔茨海默病早期认知功能干预治疗效果评估",
            "陈丽娟 教授",
            "审核中",
            "2024-01-18",
            "神经内科",
            "高",
            "BTCH-2023-087"
        );
        
        System.out.println("   ✓ 创建研究项目模型成功");
        System.out.println("   ✓ 项目标题: " + item.getTitle());
        System.out.println("   ✓ 研究者: " + item.getResearcher());
        System.out.println("   ✓ 审核状态: " + item.getStatus());
        System.out.println("   ✓ 提交日期: " + item.getDate());
        System.out.println("   ✓ 所属科室: " + item.getDepartment());
        System.out.println("   ✓ 优先级: " + item.getPriority());
        System.out.println("   ✓ 伦理编号: " + item.getEthicsNumber());
        System.out.println("   ✓ 显示文本: " + item.toString());
        System.out.println();
        
        // 测试状态系统
        testStatusSystem();
    }
    
    private static void testStatusSystem() {
        System.out.println("🎯 测试医学研究审核状态系统...");
        
        String[] statuses = {
            "待审核", "已通过", "已拒绝", "审核中", "暂停", "待提交", "中止"
        };
        
        String[] colors = {
            "橙色系", "绿色系", "红色系", "蓝色系", "灰色系", "橙色系", "灰色系"
        };
        
        for (int i = 0; i < statuses.length; i++) {
            ResearchItem item = new ResearchItem();
            item.setStatus(statuses[i]);
            System.out.println("   ✓ 状态: " + statuses[i] + " -> 主题色彩: " + colors[i]);
        }
        
        System.out.println("   ✓ 状态系统测试完成，支持7种审核状态");
        System.out.println();
    }
}
