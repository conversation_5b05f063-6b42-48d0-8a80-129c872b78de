package com.fox.med.qhcg;

import com.fox.med.qhcg.model.TableDataItem;
import com.fox.med.qhcg.laf.components.FlatTablePanel;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * FlatTable扁平化表格组件测试应用
 * 展示基于图片设计的现代化表格控件
 */
public class FlatTableTestApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        // 创建主布局
        VBox root = new VBox(20);
        root.setPadding(new Insets(20));
        root.setStyle("-fx-background-color: #f8fafc;");

        // 创建标题
        Label titleLabel = new Label("FlatTable 扁平化表格组件演示");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1e3a8a;");

        // 创建化学元素表格
        FlatTablePanel elementsTable = createElementsTable();
        
        // 创建医学数据表格
        FlatTablePanel medicalTable = createMedicalDataTable();

        // 添加到布局
        root.getChildren().addAll(titleLabel, elementsTable, medicalTable);

        // 创建场景
        Scene scene = new Scene(root, 1000, 700);
        
        // 加载CSS样式
        scene.getStylesheets().add(getClass().getResource("/styles/header-list.css").toExternalForm());

        // 设置舞台
        primaryStage.setTitle("医学研究管理系统 - FlatTable组件演示");
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    /**
     * 创建化学元素表格 - 基于图片中的数据
     */
    private FlatTablePanel createElementsTable() {
        FlatTablePanel table = new FlatTablePanel("化学元素周期表 (前5个元素)");
        
        // 添加示例数据 - 与图片中的数据一致
        table.addItems(
            new TableDataItem("1", "Hydrogen", "1.0079", "H"),
            new TableDataItem("2", "Helium", "4.0026", "He"),
            new TableDataItem("3", "Lithium", "6.941", "Li"),
            new TableDataItem("4", "Beryllium", "9.0122", "Be"),
            new TableDataItem("5", "Boron", "10.811", "B")
        );
        
        // 设置表格高度
        table.getTableView().setPrefHeight(250);
        
        return table;
    }

    /**
     * 创建医学数据表格 - 展示在医学研究中的应用
     */
    private FlatTablePanel createMedicalDataTable() {
        FlatTablePanel table = new FlatTablePanel("医学研究项目统计数据");
        
        // 自定义列标题以适应医学数据
        table.getNumberColumn().setText("编号");
        table.getNameColumn().setText("项目名称");
        table.getWeightColumn().setText("预算(万元)");
        table.getSymbolColumn().setText("状态");
        
        // 添加医学研究数据
        table.addItems(
            new TableDataItem("001", "冠心病介入治疗研究", "150.5", "进行中"),
            new TableDataItem("002", "糖尿病药物疗效评估", "89.2", "已完成"),
            new TableDataItem("003", "肿瘤免疫治疗研究", "320.8", "审核中"),
            new TableDataItem("004", "神经退行性疾病研究", "245.0", "进行中"),
            new TableDataItem("005", "心血管疾病预防研究", "178.3", "已完成"),
            new TableDataItem("006", "儿童疫苗安全性研究", "95.7", "准备中"),
            new TableDataItem("007", "老年痴呆症治疗研究", "412.6", "进行中")
        );
        
        // 设置表格高度
        table.getTableView().setPrefHeight(300);
        
        return table;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
