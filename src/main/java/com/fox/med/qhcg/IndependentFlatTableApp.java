package com.fox.med.qhcg;

import com.fox.med.qhcg.model.TableDataItem;
import com.fox.med.qhcg.laf.components.IndependentFlatTable;
import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * 独立FlatTable组件测试应用
 * 展示完全独立的扁平化表格控件，不与HeaderList混合
 */
public class IndependentFlatTableApp extends Application {

    @Override
    public void start(Stage primaryStage) {
        // 创建主布局
        VBox root = new VBox(20);
        root.setPadding(new Insets(20));
        root.setStyle("-fx-background-color: #f8fafc;");

        // 创建标题
        Label titleLabel = new Label("独立FlatTable组件演示");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #1e3a8a;");

        // 创建说明文本
        Label descLabel = new Label("这是一个完全独立的扁平化表格控件，不与HeaderList混合");
        descLabel.setStyle("-fx-font-size: 14px; -fx-text-fill: #64748b;");

        // 创建化学元素表格 - 基于图片数据
        IndependentFlatTable elementsTable = createElementsTable();
        
        // 创建医学数据表格
        IndependentFlatTable medicalTable = createMedicalTable();

        // 添加到布局
        root.getChildren().addAll(titleLabel, descLabel, elementsTable, medicalTable);

        // 创建场景
        Scene scene = new Scene(root, 1000, 700);
        
        // 加载CSS样式
        scene.getStylesheets().add(getClass().getResource("/styles/header-list.css").toExternalForm());

        // 设置舞台
        primaryStage.setTitle("医学研究管理系统 - 独立FlatTable组件");
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    /**
     * 创建化学元素表格 - 完全基于图片中的数据
     */
    private IndependentFlatTable createElementsTable() {
        IndependentFlatTable table = new IndependentFlatTable();
        
        // 添加图片中的化学元素数据
        table.addItems(
            new TableDataItem("1", "Hydrogen", "1.0079", "H"),
            new TableDataItem("2", "Helium", "4.0026", "He"),
            new TableDataItem("3", "Lithium", "6.941", "Li"),
            new TableDataItem("4", "Beryllium", "9.0122", "Be"),
            new TableDataItem("5", "Boron", "10.811", "B")
        );
        
        // 设置表格高度
        table.setTableHeight(200);
        
        return table;
    }

    /**
     * 创建医学数据表格 - 展示在医学研究中的应用
     */
    private IndependentFlatTable createMedicalTable() {
        IndependentFlatTable table = new IndependentFlatTable();
        
        // 自定义列标题以适应医学数据
        table.getNumberColumn().setText("编号");
        table.getNameColumn().setText("检查项目");
        table.getWeightColumn().setText("正常值");
        table.getSymbolColumn().setText("单位");
        
        // 添加医学检查数据
        table.addItems(
            new TableDataItem("001", "血红蛋白", "120-160", "g/L"),
            new TableDataItem("002", "白细胞计数", "4.0-10.0", "×10⁹/L"),
            new TableDataItem("003", "血小板计数", "100-300", "×10⁹/L"),
            new TableDataItem("004", "总胆固醇", "3.1-5.7", "mmol/L"),
            new TableDataItem("005", "血糖", "3.9-6.1", "mmol/L"),
            new TableDataItem("006", "肌酐", "44-133", "μmol/L"),
            new TableDataItem("007", "尿酸", "208-428", "μmol/L"),
            new TableDataItem("008", "谷丙转氨酶", "9-50", "U/L")
        );
        
        // 设置表格高度
        table.setTableHeight(280);
        
        return table;
    }

    public static void main(String[] args) {
        launch(args);
    }
}
