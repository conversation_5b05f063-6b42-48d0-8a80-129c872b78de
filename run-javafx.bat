@echo off
echo 启动医学研究管理系统 - HeaderList控件演示

echo.
echo 正在编译项目...
call mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 编译成功！正在启动JavaFX应用...
echo.

REM 使用JavaFX Maven插件运行
call mvn javafx:run

if %ERRORLEVEL% neq 0 (
    echo.
    echo JavaFX应用启动失败，尝试使用备用方法...
    echo.
    
    REM 备用方法：直接使用java命令
    java --module-path "%USERPROFILE%\.m2\repository\org\openjfx\javafx-controls\21.0.1;%USERPROFILE%\.m2\repository\org\openjfx\javafx-base\21.0.1;%USERPROFILE%\.m2\repository\org\openjfx\javafx-graphics\21.0.1;%USERPROFILE%\.m2\repository\org\openjfx\javafx-fxml\21.0.1" --add-modules javafx.controls,javafx.fxml -cp target\classes com.fox.med.qhcg.MedResearchApp
)

echo.
echo 应用已关闭
pause
