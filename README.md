# 医学研究管理系统

基于JavaFX的医学研究伦理审查管理系统，提供专业的UI组件和数据管理功能。

## 特性

- 医学专业主题设计
- 7种审核状态可视化
- 响应式布局
- JavaFX数据绑定

## 组件

- **TitledListPanel** - 带标题的列表面板
- **EthicsReviewListPanel** - 伦理审查专用列表
- **FlatTablePanel** - 带标题的扁平化表格组件
- **FlatTable** - 简洁的扁平化表格控件
- **EthicsReviewProject** - 项目数据模型
- **TableDataItem** - 表格数据模型
- **EthicsReviewProjectRenderer** - 列表渲染器
- **EthicsReviewDashboardPanel** - 主仪表板

## 快速开始

### 运行应用
```bash
mvn clean compile
mvn javafx:run
```

### 基础用法
```java
// 创建列表面板
TitledListPanel list = new TitledListPanel("科室列表");
list.addItems("心内科", "神经内科");

// 创建项目列表
EthicsReviewListPanel projectList = new EthicsReviewListPanel("项目");
projectList.setCellFactory(listView -> new EthicsReviewProjectRenderer());

// 创建带标题的扁平化表格
FlatTablePanel table = new FlatTablePanel("数据表格");
table.addItem(new TableDataItem("1", "项目名称", "数值", "状态"));

// 创建简洁的扁平化表格
FlatTable simpleTable = new FlatTable();
simpleTable.addItem(new TableDataItem("1", "Hydrogen", "1.0079", "H"));
```

### 其他运行选项
```bash
# 运行仪表板
mvn javafx:run@dashboard

# 运行测试应用
mvn javafx:run@test-improved

# 运行FlatTable演示
mvn javafx:run@flat-table

# 运行简洁FlatTable演示
mvn javafx:run@simple-table
```

## 技术栈

- Java 17 + JavaFX 21.0.1 + Maven
- 医学专业主题CSS样式
- JavaFX模块系统
