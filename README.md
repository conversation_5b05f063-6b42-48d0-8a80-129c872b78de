# 医学研究管理系统

基于JavaFX的医学研究伦理审查管理系统，提供专业的UI组件和数据管理功能。

## 特性

- 医学专业主题设计
- 7种审核状态可视化
- 响应式布局
- JavaFX数据绑定

## 组件

- **HeaderList** - 标题列表组件
- **EthicsReviewList** - 伦理审查专用列表
- **FlatTablePanel** - 带标题的扁平化表格组件
- **FlatTable** - 简洁的扁平化表格控件
- **EthicsReviewProject** - 项目数据模型
- **TableDataItem** - 表格数据模型
- **ReviewState** - 审核状态枚举
- **EthicsReviewProjectRenderer** - 列表渲染器
- **EthicsReviewDashboardPanel** - 主仪表板

## 快速开始

### 运行统一测试应用
```bash
mvn clean compile
mvn javafx:run
```

### 组件使用示例
```java
// 创建标题列表组件
HeaderList list = new HeaderList("科室列表");
list.addItems("心内科", "神经内科");

// 创建项目列表
EthicsReviewList projectList = new EthicsReviewList("项目");
projectList.setCellFactory(listView -> new EthicsReviewProjectRenderer());

// 创建带标题的扁平化表格
FlatTablePanel table = new FlatTablePanel("数据表格");
table.addItem(new TableDataItem("1", "项目名称", "数值", "状态"));

// 创建简洁的扁平化表格
FlatTable simpleTable = new FlatTable();
simpleTable.addItem(new TableDataItem("1", "Hydrogen", "1.0079", "H"));

// 使用ReviewState枚举
EthicsReviewProject project = new EthicsReviewProject("项目", "研究者", "待审核", "2024-01-01", "科室");
project.setReviewState(ReviewState.APPROVED);
String cssClass = project.getStatusCssClass();
```

### 演示应用特性
- **Tab界面**: 统一的标签页展示所有组件
- **列表组件Tab**: 展示HeaderList和EthicsReviewList
- **表格组件Tab**: 展示FlatTable组件
- **综合仪表板Tab**: 完整的医学研究管理界面

### 运行方式
```bash
# 运行组件演示（默认）
mvn javafx:run

# 或者明确指定演示应用
mvn javafx:run@demo
```

## 样式架构

项目采用模块化CSS架构，便于维护和扩展：

### CSS文件结构
```
src/main/resources/styles/
├── main.css                    # 主样式文件
├── global.css                  # 全局样式
├── header-list.css             # HeaderList组件样式
├── ethics-review.css           # EthicsReviewList组件样式
└── table.css                   # FlatTable组件样式
```

### 样式特性
- **极简设计**: 移除不必要的装饰效果
- **高性能**: 精简的CSS代码，快速加载
- **模块化**: 每个组件独立的CSS文件
- **易维护**: 清晰的文件组织和简洁的代码

## 技术栈

- Java 17 + JavaFX 21.0.1 + Maven
- 医学专业主题CSS样式
- JavaFX模块系统
