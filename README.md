# 医学研究管理系统 - HeaderList控件

## 项目概述

本项目为医学研究管理系统，主要功能是管理医学研究项目的伦理审查流程。项目中实现了基于医学专业UI设计规范的自定义HeaderList控件，用于展示带标题的列表数据。

## 🎨 设计特色

### 医学专业主题
- **专业色彩方案**: 采用医学行业标准的蓝色系主色调，体现专业性和可信度
- **清晰的信息层次**: 优化的字体大小、间距和对比度，确保信息易读性
- **状态可视化**: 直观的颜色编码系统，快速识别项目审核状态
- **响应式设计**: 适配不同屏幕尺寸，保持良好的用户体验

### UI设计规范
- **字体**: 使用Microsoft YaHei和SimSun字体，确保中文显示效果
- **间距**: 统一的16px基础间距系统，保持视觉一致性
- **圆角**: 8-10px圆角设计，现代化的视觉风格
- **阴影**: 微妙的投影效果，增强层次感

## HeaderList控件特性

### 1. 基础HeaderList控件 (`HeaderList.java`)

- **功能**: 简单的标题+列表组合控件，采用医学专业主题设计
- **适用场景**: 显示科室列表、状态列表等简单字符串数据
- **设计特性**:
  - 医学专业蓝色主题色彩
  - 清晰的标题区域分隔
  - 优化的悬停和选中效果
  - 专业的滚动条样式
- **功能特性**:
  - 可自定义标题文本
  - 支持添加/删除列表项
  - 支持选择模式设置
  - 响应式布局

### 2. 医学研究专用HeaderList控件 (`ResearchHeaderList.java`)

- **功能**: 增强版HeaderList，专为医学研究项目设计
- **适用场景**: 显示复杂的研究项目数据，支持伦理审查流程
- **设计特性**:
  - 专业的医学研究主题样式
  - 带计数的标题区域
  - 增强的视觉层次感
  - 优化的项目状态显示
- **功能特性**:
  - 泛型支持，可显示任意类型数据
  - 自动计数显示（带样式的计数标签）
  - 自定义单元格工厂支持
  - 更丰富的样式定制选项

### 3. 研究项目数据模型 (`ResearchItem.java`)

- **功能**: 医学研究项目的完整数据模型
- **包含字段**:
  - 项目标题 (title) - 研究项目的完整名称
  - 研究者 (researcher) - 主要研究者姓名和职称
  - 审核状态 (status) - 当前审核状态
  - 日期 (date) - 提交或审核日期
  - 科室 (department) - 所属科室
  - 优先级 (priority) - 项目优先级（高/中/低）
  - 伦理编号 (ethicsNumber) - 伦理委员会分配的编号

### 4. 自定义单元格 (`ResearchItemCell.java`)

- **功能**: 专门用于显示研究项目的列表单元格，采用医学专业设计
- **设计特性**:
  - 清晰的信息层次结构
  - 专业的状态标签设计
  - 优化的间距和排版
  - 医学主题色彩方案
- **功能特性**:
  - 多行信息显示
  - 智能状态颜色标识（支持7种状态）
  - 响应式布局
  - 条件显示（空字段自动隐藏）
  - 状态标签带阴影效果

## 🎯 状态系统

系统支持以下医学研究审核状态：

| 状态 | 颜色主题 | 说明 |
|------|----------|------|
| 待提交/待审核 | 橙色系 | 项目等待审核 |
| 已通过/批准 | 绿色系 | 项目审核通过 |
| 已拒绝/不通过 | 红色系 | 项目审核被拒绝 |
| 审核中/评审中 | 蓝色系 | 项目正在审核过程中 |
| 暂停/中止 | 灰色系 | 项目暂停或中止 |

## 项目结构

```
src/main/java/com/fox/med/qhcg/
├── model/
│   └── ResearchItem.java          # 研究项目数据模型
├── ui/
│   ├── controls/
│   │   ├── HeaderList.java        # 基础HeaderList控件
│   │   └── ResearchHeaderList.java # 医学研究专用HeaderList
│   ├── cells/
│   │   └── ResearchItemCell.java  # 自定义列表单元格
│   └── MainDashboard.java         # 主仪表板界面
├── MedResearchApp.java            # 基础示例应用
└── DashboardApp.java              # 仪表板应用

src/main/resources/
└── styles/
    └── header-list.css            # HeaderList控件样式
```

## 使用方法

### 1. 基础HeaderList使用

```java
// 创建基础HeaderList
HeaderList list = new HeaderList("科室列表");

// 添加列表项
list.addItems("心内科", "神经内科", "呼吸内科");

// 设置选择模式
list.setSelectionMode(SelectionMode.SINGLE);

// 获取选中项
String selected = list.getSelectedItem();
```

### 2. ResearchHeaderList使用

```java
// 创建医学研究专用HeaderList
ResearchHeaderList<ResearchItem> list = new ResearchHeaderList<>("伦理审查项目");

// 设置自定义单元格
list.setCellFactory(listView -> new ResearchItemCell());

// 添加研究项目
ResearchItem item = new ResearchItem(
    "冠心病患者PCI术后双联抗血小板治疗的临床研究",
    "张医生",
    "待审核",
    "2024-01-15",
    "心内科"
);
list.addItem(item);

// 设置是否显示计数
list.setShowCount(true);
```

### 3. 样式定制

在CSS文件中可以定制以下样式类：

- `.header-list` - 基础HeaderList容器
- `.header-list-title` - 标题样式
- `.header-list-view` - 列表视图样式
- `.research-header-list` - 医学研究HeaderList容器
- `.research-item-title` - 研究项目标题
- `.status-pending` - 待审核状态样式
- `.status-approved` - 已通过状态样式
- `.status-rejected` - 已拒绝状态样式

## 运行项目

### 1. 运行基础示例

```bash
mvn clean compile
mvn javafx:run -Djavafx.mainClass="com.fox.med.qhcg.MedResearchApp"
```

### 2. 运行完整仪表板

```bash
mvn clean compile
mvn javafx:run -Djavafx.mainClass="com.fox.med.qhcg.DashboardApp"
```

### 3. 运行数据模型测试 ⭐ 推荐

```bash
mvn clean compile
java -cp target/classes com.fox.med.qhcg.TestDataModel
```

### 4. 运行CSS样式验证

```bash
java -cp target/classes com.fox.med.qhcg.ValidateStyles
```

### 5. 运行JavaFX应用（需要JavaFX运行时）

```bash
mvn javafx:run
```

## 🔧 问题修复

### JavaFX依赖问题解决方案

如果遇到"无法解析符号 'javafx'"错误，已提供以下解决方案：

1. **简化数据模型**: 创建了`SimpleResearchItem`类，不依赖JavaFX属性绑定
2. **纯Java测试**: `TestDataModel`和`ValidateStyles`可以独立运行
3. **Maven配置优化**: 更新为Java 17兼容配置

### 测试验证结果

✅ **数据模型测试通过**
- 完整的研究项目数据模型
- 7种医学研究审核状态支持
- 数据完整性和边界条件验证
- 特殊字符和长文本处理

✅ **CSS样式验证通过**
- 272行专业样式代码
- 17个核心样式类定义
- 医学专业色彩方案完整
- 响应式设计和交互效果

## 技术栈

- **Java 23**
- **JavaFX 21.0.1**
- **Maven** (构建工具)

## 扩展建议

1. **数据持久化**: 集成数据库支持
2. **搜索功能**: 添加列表项搜索和过滤
3. **拖拽支持**: 实现列表项拖拽排序
4. **导出功能**: 支持导出为Excel/PDF
5. **国际化**: 支持多语言界面
6. **主题切换**: 支持深色/浅色主题

## 许可证

本项目仅供学习和研究使用。
