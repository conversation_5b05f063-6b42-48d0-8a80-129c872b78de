# 医学研究管理系统 - HeaderList控件

## 项目概述

医学研究管理系统的自定义HeaderList控件，基于JavaFX和医学专业UI设计规范，用于展示医学研究项目的伦理审查数据。

## 核心特性

- **医学专业主题**: 蓝色系主色调，专业的视觉设计
- **状态可视化**: 7种医学研究审核状态的颜色编码
- **响应式设计**: 适配不同屏幕尺寸
- **数据绑定**: JavaFX属性绑定支持

## 主要组件

### LAF (Look and Feel) 组件
- **TitledListPanel**: 带标题的基础列表面板
- **EthicsReviewListPanel**: 医学研究伦理审查专用列表面板

### 数据模型
- **EthicsReviewProject**: 医学研究伦理审查项目数据模型

### 渲染器
- **EthicsReviewProjectRenderer**: 伦理审查项目的自定义列表渲染器

### 面板
- **EthicsReviewDashboardPanel**: 伦理审查主仪表板面板

### 状态系统
支持7种医学研究审核状态：待审核、已通过、已拒绝、审核中、暂停等

## 快速使用

### 基础用法

```java
// 创建HeaderList
HeaderList list = new HeaderList("科室列表");
list.addItems("心内科", "神经内科", "呼吸内科");

// 创建研究项目列表
ResearchHeaderList<ResearchItem> researchList = new ResearchHeaderList<>("伦理审查项目");
researchList.setCellFactory(listView -> new ResearchItemCell());
researchList.addItem(new ResearchItem("项目标题", "研究者", "待审核", "2024-01-15", "心内科"));
```

## 运行项目

```bash
# 编译项目
mvn clean compile

# 运行基础示例
mvn javafx:run

# 运行仪表板
mvn javafx:run@dashboard

# 运行改进版测试
mvn javafx:run@test-improved
```

## 技术栈

- **Java 17** + **JavaFX 21.0.1** + **Maven**
- **CSS样式**: 272行医学专业主题
- **模块系统**: 完整的module-info.java配置
