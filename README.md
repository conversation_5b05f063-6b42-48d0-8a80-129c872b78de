# 医学研究管理系统 - HeaderList控件

## 项目概述

本项目为医学研究管理系统，主要功能是管理医学研究项目的伦理审查流程。项目中实现了自定义的HeaderList控件，用于展示带标题的列表数据。

## HeaderList控件特性

### 1. 基础HeaderList控件 (`HeaderList.java`)

- **功能**: 简单的标题+列表组合控件
- **适用场景**: 显示简单的字符串列表
- **特性**:
  - 可自定义标题文本
  - 支持添加/删除列表项
  - 支持选择模式设置
  - 响应式布局

### 2. 医学研究专用HeaderList控件 (`ResearchHeaderList.java`)

- **功能**: 增强版HeaderList，支持泛型数据模型
- **适用场景**: 显示复杂的研究项目数据
- **特性**:
  - 泛型支持，可显示任意类型数据
  - 自动计数显示
  - 自定义单元格工厂支持
  - 更丰富的样式定制

### 3. 研究项目数据模型 (`ResearchItem.java`)

- **功能**: 医学研究项目的数据模型
- **包含字段**:
  - 项目标题 (title)
  - 研究者 (researcher)
  - 审核状态 (status)
  - 日期 (date)
  - 科室 (department)

### 4. 自定义单元格 (`ResearchItemCell.java`)

- **功能**: 专门用于显示研究项目的列表单元格
- **特性**:
  - 多行信息显示
  - 状态颜色标识
  - 响应式布局
  - 条件显示（空字段自动隐藏）

## 项目结构

```
src/main/java/com/fox/med/qhcg/
├── model/
│   └── ResearchItem.java          # 研究项目数据模型
├── ui/
│   ├── controls/
│   │   ├── HeaderList.java        # 基础HeaderList控件
│   │   └── ResearchHeaderList.java # 医学研究专用HeaderList
│   ├── cells/
│   │   └── ResearchItemCell.java  # 自定义列表单元格
│   └── MainDashboard.java         # 主仪表板界面
├── MedResearchApp.java            # 基础示例应用
└── DashboardApp.java              # 仪表板应用

src/main/resources/
└── styles/
    └── header-list.css            # HeaderList控件样式
```

## 使用方法

### 1. 基础HeaderList使用

```java
// 创建基础HeaderList
HeaderList list = new HeaderList("科室列表");

// 添加列表项
list.addItems("心内科", "神经内科", "呼吸内科");

// 设置选择模式
list.setSelectionMode(SelectionMode.SINGLE);

// 获取选中项
String selected = list.getSelectedItem();
```

### 2. ResearchHeaderList使用

```java
// 创建医学研究专用HeaderList
ResearchHeaderList<ResearchItem> list = new ResearchHeaderList<>("伦理审查项目");

// 设置自定义单元格
list.setCellFactory(listView -> new ResearchItemCell());

// 添加研究项目
ResearchItem item = new ResearchItem(
    "冠心病患者PCI术后双联抗血小板治疗的临床研究",
    "张医生",
    "待审核",
    "2024-01-15",
    "心内科"
);
list.addItem(item);

// 设置是否显示计数
list.setShowCount(true);
```

### 3. 样式定制

在CSS文件中可以定制以下样式类：

- `.header-list` - 基础HeaderList容器
- `.header-list-title` - 标题样式
- `.header-list-view` - 列表视图样式
- `.research-header-list` - 医学研究HeaderList容器
- `.research-item-title` - 研究项目标题
- `.status-pending` - 待审核状态样式
- `.status-approved` - 已通过状态样式
- `.status-rejected` - 已拒绝状态样式

## 运行项目

### 1. 运行基础示例

```bash
mvn clean compile
mvn javafx:run -Djavafx.mainClass="com.fox.med.qhcg.MedResearchApp"
```

### 2. 运行完整仪表板

```bash
mvn clean compile
mvn javafx:run -Djavafx.mainClass="com.fox.med.qhcg.DashboardApp"
```

## 技术栈

- **Java 23**
- **JavaFX 21.0.1**
- **Maven** (构建工具)

## 扩展建议

1. **数据持久化**: 集成数据库支持
2. **搜索功能**: 添加列表项搜索和过滤
3. **拖拽支持**: 实现列表项拖拽排序
4. **导出功能**: 支持导出为Excel/PDF
5. **国际化**: 支持多语言界面
6. **主题切换**: 支持深色/浅色主题

## 许可证

本项目仅供学习和研究使用。
